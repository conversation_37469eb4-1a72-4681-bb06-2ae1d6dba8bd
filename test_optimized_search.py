#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的 search_cases_by_entity 方法
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import SessionLocal
from app.services.scene_qz.scene_qz_service import SceneQzService
from app.log.log_utils import LogUtils


async def test_search_cases_by_entity():
    """测试优化后的实体查询方法"""
    
    # 测试用例
    test_cases = [
        {
            "name": "张三",
            "id_card": "******************",
            "description": "有身份证和姓名的查询"
        },
        {
            "name": "王五", 
            "id_card": None,
            "description": "仅姓名的查询"
        },
        {
            "name": "李四",
            "id_card": "******************", 
            "description": "另一个有身份证的查询"
        }
    ]
    
    db = SessionLocal()
    try:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"测试用例 {i}: {test_case['description']}")
            print(f"姓名: {test_case['name']}")
            print(f"身份证: {test_case['id_card']}")
            print(f"{'='*60}")
            
            try:
                # 调用优化后的方法
                result = await SceneQzService.search_cases_by_entity(
                    id_card=test_case['id_card'],
                    name=test_case['name'],
                    page=1,
                    page_size=10,
                    db=db
                )
                
                print(f"查询结果:")
                print(f"  总数: {result['total']}")
                print(f"  当前页: {result['page']}")
                print(f"  每页大小: {result['page_size']}")
                print(f"  返回条目数: {len(result['items'])}")
                
                # 显示详细结果
                for j, item in enumerate(result['items'], 1):
                    print(f"\n  聚类 {j}:")
                    print(f"    姓名: {item['name']}")
                    print(f"    身份证: {item['id_card']}")
                    print(f"    案件数量: {len(item['cases'])}")
                    
                    for k, case in enumerate(item['cases'], 1):
                        print(f"      案件 {k}: {case['case']}")
                        print(f"        角色: {case['role']}")
                        print(f"        案件类型: {case['case_type']}")
                        print(f"        时间: {case['police_time']}")
                        print(f"        地点: {case['location']}")
                
            except Exception as e:
                print(f"测试用例 {i} 执行失败: {str(e)}")
                LogUtils.error(f"测试用例 {i} 执行失败: {str(e)}")
                
    finally:
        db.close()


async def test_performance_comparison():
    """性能对比测试"""
    import time
    
    db = SessionLocal()
    try:
        test_name = "王五"
        test_id_card = None
        
        print(f"\n{'='*60}")
        print("性能测试")
        print(f"测试参数: 姓名={test_name}, 身份证={test_id_card}")
        print(f"{'='*60}")
        
        # 测试优化后的方法
        start_time = time.time()
        result = await SceneQzService.search_cases_by_entity(
            id_card=test_id_card,
            name=test_name,
            page=1,
            page_size=10,
            db=db
        )
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"优化后方法执行时间: {execution_time:.3f} 秒")
        print(f"返回结果数量: {result['total']}")
        
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        LogUtils.error(f"性能测试失败: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    print("开始测试优化后的 search_cases_by_entity 方法...")
    
    # 运行功能测试
    asyncio.run(test_search_cases_by_entity())
    
    # 运行性能测试
    asyncio.run(test_performance_comparison())
    
    print("\n测试完成!")
