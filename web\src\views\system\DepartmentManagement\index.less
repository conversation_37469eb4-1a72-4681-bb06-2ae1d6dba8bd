.app-container {
  width: 100%;
  height: 100vh;
  background: #071827;
  padding: 20px;
  box-sizing: border-box;
  color: black;
  .org-box {
    display: flex;
    margin-left: -6px;
    gap: 10px;
    color: rgba(255, 255, 255, 0.7);
  }
  .app-container-row {
    height: 100%;
    .dep-wrapper {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .key-word {
        color: #409eff;
      }
      .head-container {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 10px;
        &::-webkit-scrollbar {
          width: 0;
          display: none;
        }
      }
    }
    .no-active {
      &:active {
        font-size: 14px;
        margin: 0 20px 0 0px;
        background-color: #23415d;
        color: rgb(0, 255, 255, 0.8);
        border: none;
      }
    }

    :deep(.el-tree) {
      background: transparent;
      color: rgba(255, 255, 255, 0.7);
    }
    :deep(.el-tree__empty-block) {
      background: transparent;
    }
    :deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
      background: rgba(0, 255, 255, 0.2);
      color: #00ffff;
    }
    :deep(.el-tree-node__content:hover, .el-upload-list__item:hover) {
      background: rgba(0, 255, 255, 0.2);
      color: #00ffff;
    }
    :deep(.el-tree-node:focus > .el-tree-node__content) {
      background-color: transparent;
    }
  }

  :deep(.splitpanes.default-theme .splitpanes__pane) {
    background-color: transparent;
    height: 100%;
  }
  :deep(.splitpanes.default-theme .splitpanes__splitter) {
    background-color: #071827;
    border-left: 1px solid #06445d;
  }
  :deep(
      .splitpanes.default-theme .splitpanes__splitter:before,
      .splitpanes.default-theme .splitpanes__splitter:after
    ) {
    background-color: #06445d;
  }

  :deep(.el-radio) {
    color: rgba(255, 255, 255, 0.7);
  }
  :deep(.el-radio__input.is-checked + .el-radio__label) {
    color: #00ffff;
  }
  :deep(.el-radio__input.is-checked .el-radio__inner) {
    color: #00ffff;
    border-color: #00ffff;
    background: #00ffff;
  }
  :deep(.el-tooltip__popper.is-light) {
    background: rgba(0, 48, 102, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
  }

  :deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow) {
    border-bottom-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper.is-light[x-placement^="bottom"] .popper__arrow::after) {
    border-bottom-color: rgba(0, 48, 102, 0.95) !important;
  }

  /* Element UI 样式覆盖 */
  :deep(.el-input__inner) {
    background-color: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.7);
  }
  :deep(.vue-treeselect__control),
  :deep(.vue-treeselect__control:hover) {
    background-color: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.vue-treeselect__single-value) {
    color: rgba(255, 255, 255, 0.7);
  }
  :deep(.vue-treeselect__placeholder) {
    color: rgba(255, 255, 255, 0.5);
  }

  :deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 1);
  }

  :deep(.el-input__inner:hover),
  :deep(.el-input__inner:focus) {
    border-color: #00ffff;
  }

  :deep(.el-pagination) {
    padding: 0;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
    background-color: rgba(0, 255, 255, 0.2);
    color: #00ffff;
    border: 1px solid #00ffff;
  }

  :deep(.el-pagination.is-background .el-pager li) {
    background-color: rgba(0, 48, 102, 0.3);
    color: #ffffff;
    border: 1px solid rgba(0, 255, 255, 0.1);
    margin: 0 3px;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.disabled):hover) {
    color: #00ffff;
    border-color: #00ffff;
  }

  :deep(.el-pagination.is-background .btn-next),
  :deep(.el-pagination.is-background .btn-prev) {
    background-color: rgba(0, 48, 102, 0.3);
    color: #ffffff;
    border: 1px solid rgba(0, 255, 255, 0.1);
  }

  :deep(.el-pagination.is-background .btn-next:hover),
  :deep(.el-pagination.is-background .btn-prev:hover) {
    color: #00ffff;
    border-color: #00ffff;
  }

  :deep(.el-pagination__total),
  :deep(.el-pagination__jump),
  :deep(.el-pagination__sizes) {
    color: #ffffff;
  }

  :deep(.el-select:hover .el-input__inner) {
    border-color: #00ffff;
  }

  :deep(.el-select .el-input__inner) {
    background-color: rgba(0, 48, 102, 0.3);
    border: 1px solid rgba(0, 255, 255, 0.1);
    color: #ffffff;
  }

  :deep(.el-select-dropdown) {
    background-color: rgba(0, 48, 102, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  :deep(.el-select-dropdown__item) {
    color: #ffffff;
  }

  :deep(.el-select-dropdown__item.hover),
  :deep(.el-select-dropdown__item:hover) {
    background-color: rgba(0, 255, 255, 0.1);
    color: #00ffff;
  }

  :deep(.el-upload) {
    display: inline-block;
  }

  :deep(.el-icon-upload2),
  :deep(.el-icon-refresh),
  :deep(.el-icon-download) {
    font-size: 16px;
  }

  /* 添加tooltip样式 */
  :deep(.el-tooltip__popper) {
    background-color: rgba(0, 48, 102, 0.95) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
    font-size: 12px !important;
    padding: 8px 12px !important;
    max-width: 400px !important;
    line-height: 1.5 !important;
  }

  :deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow) {
    border-top-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="top"] .popper__arrow::after) {
    border-top-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow) {
    border-bottom-color: rgba(0, 255, 255, 0.2) !important;
  }

  :deep(.el-tooltip__popper[x-placement^="bottom"] .popper__arrow::after) {
    border-bottom-color: rgba(0, 48, 102, 0.95) !important;
  }

  :deep(.el-form-item__label) {
    color: #ffffff !important;
  }

  :deep(.el-date-editor) {
    background-color: rgba(0, 48, 102, 0.3) !important;
    border: 1px solid rgba(0, 255, 255, 0.2);
    align-items: center !important;

    .el-range__icon {
      margin-top: 3px;
    }
  }

  :deep(.el-date-editor:hover),
  :deep(.el-date-editor:focus) {
    border-color: #00ffff;
  }

  :deep(.el-form-item.el-form-item--default) {
    margin-bottom: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 10px;
  }

  /* Element UI 样式覆盖 */
  :deep(.el-loading-mask) {
    background-color: rgba(0, 24, 51, 0.9) !important;
  }

  :deep(.el-loading-spinner .el-loading-text) {
    color: #00ffff !important;
  }

  :deep(.el-loading-spinner .path) {
    stroke: #00ffff !important;
  }

  :deep(.el-date-editor .el-range-input) {
    color: #ffffff;
    background: transparent;
  }

  :deep(.el-date-editor .el-range-separator) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel) {
    background: rgba(0, 48, 102, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.2);
  }

  :deep(.el-picker-panel__content) {
    color: #ffffff;
  }

  :deep(.el-date-table th) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-date-table td.available:hover) {
    color: #00ffff;
  }

  :deep(.el-date-table td.current:not(.disabled)) {
    background-color: rgba(0, 255, 255, 0.2);
    color: #00ffff;
  }

  :deep(.el-picker-panel__shortcut) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel__shortcut:hover) {
    color: #00ffff;
  }

  :deep(.el-date-picker__header-label) {
    color: #ffffff;
  }

  :deep(.el-date-picker__header-label:hover) {
    color: #00ffff;
  }

  :deep(.el-picker-panel__icon-btn) {
    color: rgba(255, 255, 255, 0.7);
  }

  :deep(.el-picker-panel__icon-btn:hover) {
    color: #00ffff;
  }

  :deep(.el-form--inline .el-form-item__label) {
    padding-right: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    float: inline-start;
    flex-shrink: 0;
  }

  :deep(.el-select .el-input__inner) {
    padding-right: 25px;
  }

  :deep(.el-date-editor .el-range__icon) {
    line-height: 24px;
  }

  :deep(.el-date-editor .el-range__close-icon) {
    display: flex;
    align-items: center;
  }
}
