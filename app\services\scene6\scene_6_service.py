import aiohttp
from datetime import datetime, timedelta
from typing import Any, List, Dict

from sqlalchemy import text, and_, or_, case
from sqlalchemy.orm import Session

from app.core.context_handle import UserContext
from app.core.task_queue import TaskQueueManager
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.org import Org
from app.models.scene_6_po import scene_6_record
from app.services.user.org_service import OrgService


class Scene6Service:

    def __init__(self):
        super().__init__()
        # 注册场景5分析队列
        self.analyze_queue_name = "scene_6_analyze"
        # 注册场景5同步队列
        self.sync_queue_name = "scene_6_sync"
        # 异步分析队列
        self.queue_manager = TaskQueueManager()

    async def async_init(self):
        # 异步初始化代码
        await self.queue_manager.register_queue(
            queue_name=self.sync_queue_name,
            processor=self._sync,
            max_size=1000,  # 队列容量
            concurrency=20  # 最大并发数
        )
        await self.queue_manager.register_queue(
            queue_name=self.analyze_queue_name,
            processor=self._do_analyze,
            max_size=1000,  # 队列容量
            concurrency=20  # 最大并发数
        )

    @staticmethod
    @transactional
    async def scene_6_search(search_params: Dict[str, Any], db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"开始查询数据，参数: {search_params}")
        try:
            # 构建查询条件
            query = db.query(scene_6_record)
            if search_params.get('finance_type') == 0:
                query = query.filter(scene_6_record.analysis_result == '入库超期')
            elif search_params.get('finance_type') == 1:
                query = query.filter(scene_6_record.analysis_result == '出库超期')
            conditions = []

            # 添加查询条件
            if search_params.get('ajbh'):
                conditions.append(scene_6_record.case_code == search_params['ajbh'])
            if search_params.get('cwmc'):
                # 案件名称模糊搜索
                conditions.append(scene_6_record.measure_object_name.like(f"%{search_params['cwmc']}%"))
            if search_params.get('djdwmc'):
                conditions.append(scene_6_record.handle_org_code == search_params['djdwmc'])
            if search_params.get('start_date'):
                start_date = search_params['start_date']
                conditions.append(scene_6_record.mod_time >= start_date)
            if search_params.get('end_date'):
                end_date = search_params['end_date']
                conditions.append(scene_6_record.mod_time <= end_date)
            # 应用查询条件
            if conditions:
                query = query.filter(and_(*conditions))
            # 获取当前用户信息
            current_org_code = UserContext.get_current_user_org().get("org_code")
            police_number = UserContext.current_user.get().police_number
            # 查询当前用户机构权限
            org_info = db.query(Org).filter(Org.org_code == current_org_code).first()
            # 构建机构和警号的查询条件
            org_condition = (
                scene_6_record.org_code.like(f"{current_org_code}%")
                if org_info and org_info.can_sub_query == 1
                else scene_6_record.org_code == current_org_code
            )
            # 组合查询条件
            query = query.filter(
                or_(
                    org_condition,
                    scene_6_record.org_code == f"-{police_number}"
                )
            )

            # 获取总记录数
            total = query.count()
            LogUtils.info(f"查询到总记录数: {total}")
            page = search_params.get('page')
            page_size = search_params.get('page_size')

            if page is None and page_size is None:
                items = query.all()
            else:
                page = 1 if page is None else page
                page_size = 10 if page_size is None else page_size
                # 分页参数
                offset = (page - 1) * page_size

                # 获取分页数据，
                items = query.offset(offset).limit(page_size).all()
                LogUtils.info(f"获取到第 {page} 页数据，每页 {page_size} 条")

            # 转换结果为字典列表
            result_items = []
            for item in items:
                item_dict = {k: v for k, v in vars(item).items() if not k.startswith('_')}
                result_items.append(item_dict)

            return {
                "total": total,
                "items": result_items,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"查询数据时出错: {str(e)}")
            raise Exception(f"查询数据时出错: {str(e)}")

    @transactional
    async def _do_analyze(self, task_data: dict, db: Session = None):
        """涉案财务入库出库超期情况分析"""
        ids = task_data['ids']
        # 批量查询所有记录
        records = db.query(scene_6_record).filter(scene_6_record.id.in_(ids)).all()
        if not records:
            return

        fail_num = 0
        batch_size = 50
        for i, record in enumerate(records, 1):
            try:
                now_time = datetime.now().strftime("%Y%m%d%H%M%S")
                three_days_ago = (datetime.now() - timedelta(days=3)).strftime("%Y%m%d%H%M%S")
                if record.current_status == '扣押':
                    if record.mod_time is not None and len(record.mod_time) == 8:
                        mod_time = record.mod_time + "000000"
                    else:
                        continue
                    if record.mod_time and len(mod_time) == 8 and int(mod_time) < int(three_days_ago):
                        record.analysis_result = '入库超期'
                        # 将时间字符串转为datetime对象计算天数差
                        mod_time_dt = datetime.strptime(mod_time, "%Y%m%d%H%M%S")
                        three_days_ago_dt = datetime.strptime(three_days_ago, "%Y%m%d%H%M%S")
                        record.overdue_time = str((three_days_ago_dt - mod_time_dt).days)
                    else:
                        continue
                elif record.current_status == '入库':
                    if record.storage_expire_time:
                        storage_expire_time = record.storage_expire_time
                        if len(storage_expire_time) == 8:
                            storage_expire_time = storage_expire_time + '000000'
                        else:
                            continue
                        if int(storage_expire_time) < int(now_time):
                            record.analysis_result = '出库超期'
                            # 将时间字符串转为datetime对象计算天数差
                            now_time_dt = datetime.strptime(now_time, "%Y%m%d%H%M%S")
                            storage_expire_time_dt = datetime.strptime(storage_expire_time, "%Y%m%d%H%M%S")
                            record.overdue_time = str((now_time_dt - storage_expire_time_dt).days)
                elif record.current_status == '延期':
                    if record.storage_expire_time and record.extension_period:
                        storage_expire_time = record.storage_expire_time
                        if len(storage_expire_time) == 8:
                            storage_expire_time = storage_expire_time + '000000'
                        else:
                            continue
                        expire_time = datetime.strptime(storage_expire_time, "%Y%m%d%H%M%S")
                        new_expire_time = expire_time + timedelta(days=int(record.extension_period))
                        new_expire_time_str = new_expire_time.strftime("%Y%m%d%H%M%S")
                        if int(new_expire_time_str) < int(now_time):
                            record.analysis_result = '出库超期'
                            # 将时间字符串转为datetime对象计算天数差
                            now_time_dt = datetime.strptime(now_time, "%Y%m%d%H%M%S")
                            new_expire_time_dt = datetime.strptime(new_expire_time_str, "%Y%m%d%H%M%S")
                            record.overdue_time = str((now_time_dt - new_expire_time_dt).days)
                elif record.current_status == '出库':
                    record.analysis_result = '未超期'
                record.analysis_status = 2
            except Exception as e:
                record.analysis_status = 3
                fail_num += 1
                LogUtils.error(f"id{record.id}分析过程中发生错误: {str(e)}")
            if i % batch_size == 0 or i == len(ids):
                db.merge(record)
                db.commit()
        LogUtils.info(f"涉案财务数据分析完成, 总数{len(ids)}个，成功{len(ids) - fail_num}个，失败{fail_num}个")

    @transactional
    async def scene_6_analyze(self, user_id: int, ids: [int] = None, db: Session = None):  # 修改默认值为None
        """分析涉案财务案件数据"""
        if not ids:
            records = db.query(scene_6_record.id).filter(
                or_(
                    scene_6_record.analysis_status == 0,
                    scene_6_record.analysis_status == 3
                )
            ).all()
            ids = [record.id for record in records]

        length_ids = len(ids) if ids else 0
        LogUtils.info(f"开始分析数据，用户ID: {user_id}，总数: {length_ids}")
        if length_ids == 0:
            return

        # 将ID列表50分批次处理
        step = 50
        batch_size = max(length_ids // step, 1)  # 确保batch_size至少为1
        for i in range(0, len(ids), batch_size):
            batch_ids = ids[i:i + batch_size]
            task_param = {
                'ids': batch_ids,
            }
            await self.queue_manager.add_task(self.analyze_queue_name, task_param)

    @transactional
    async def _sync(self, task_data: dict, db: Session = None):

        listData = task_data["listData"]
        if not listData:
            return
        user_id = task_data["user_id"]
        total = len(listData)
        success = 0
        org_map = OrgService(db).get_org_map(db)

        batch_size = 50
        for i, data in enumerate(listData, 1):
            # 更新或创建分析记录
            ajbh = data.get('ajbh', '')
            existing_record = db.query(scene_6_record).filter(scene_6_record.case_code == ajbh).first()
            if existing_record:
                # 判断 ods_aj_flws_dxglb 中的 pzsj 是否大于 existing_record.measure_approve_date ,如果大于则删除 existing_record
                ods_pzsj = int(data.get('pzsj')) if data.get('pzsj') else 0
                existing_pzsj = int(existing_record.mod_time) if existing_record.mod_time else 0
                # 当任一时间戳为空或新数据时间戳更新时，更新记录
                if not ods_pzsj or not existing_pzsj or ods_pzsj > existing_pzsj:
                    # 重置记录状态
                    existing_record.user_id = user_id
                    existing_record.analysis_status = 0
                    existing_record.analysis_result = None
                    new_record = existing_record
                else:
                    continue
            else:
                new_record = scene_6_record(
                    user_id=user_id,
                    analysis_status=0,
                    analysis_result=None,
                )
                LogUtils.info(f"创建新案件记录: ajbh={ajbh}")
            try:
                # data如果没有jlbh 则设置为 ''
                # data如果没有jlbh 则设置为 ''
                # 更新记录字段
                new_record.case_name = data.get('ajmc', '')
                new_record.case_code = data.get('ajbh', '')
                new_record.handle_org_code = data.get('djdwmc', '')
                new_record.hoster_name = data.get('djrxm', '')
                new_record.measure_code = data.get('jdjg', '')
                new_record.measure_type = "收缴" if data.get('jdjg', '') == '240500' else "扣押"
                new_record.measure_object_name = data.get('mc', '')
                new_record.mod_time = data.get('pzsj') if data.get('pzsj') else None

                # 确保FinanceData是已经解析完成的数据
                finance_data = task_data["FinanceData"].get(ajbh, [{}, {}, {}, None])
                new_record.registration_time = finance_data[0].get("djsj", '')
                new_record.registration_quantity = finance_data[0].get("cwsl", '')
                new_record.storage_period = finance_data[0].get("gl_fdqx", '')
                new_record.storage_expire_time = finance_data[0].get("gl_bgdqsj", '')
                new_record.adjust_time = finance_data[0].get("moder_time", '')
                new_record.extension_registration_date = finance_data[1].get("czsj", '')
                new_record.extension_period = finance_data[1].get("clqx", '')
                new_record.operation_time = finance_data[2].get("czsj", '')
                new_record.current_status = finance_data[3]

                new_record.org_code= org_map.get(data.get('djdw', ''), "0000")

                success += 1
                if i % batch_size == 0 or i == len(listData):
                    db.merge(new_record)
                    db.commit()

            except Exception as e:
                LogUtils.error(f"插入案件 {ajbh} 出错: {str(e)}")
                continue

        LogUtils.info(f"数据同步完成，总数: {total}, 成功: {success}, 失败: {total - success}")

    @transactional
    async def scene_6_data_sync(self, user_id: int, start_date: str, end_date: str, db: Session = None) -> bool:
        """同步案件数据"""

        LogUtils.info(f"开始同步数据，用户ID: {user_id}")
        try:
            LogUtils.info(f"查询时间范围: {start_date} 至 {end_date}")
            
            # 处理时间格式，确保包含时分秒
            if len(start_date) == 8:  # YYYYMMDD格式
                start_date = start_date + '000000'  # 添加00时00分00秒
            if len(end_date) == 8:  # YYYYMMDD格式
                end_date = end_date + '235959'  # 添加23时59分59秒

            # 查询行政案件和刑事案件编号
            main_data = self._query_main_(start_date, end_date, db)
            main_data = self._query_other_(main_data, db)
            finance_data = self._query_finance_(main_data, db)
            
            task_data = {
                "listData": main_data,
                "FinanceData": finance_data,
                "user_id": user_id
            }
            sync_result = await self.queue_manager.add_task(self.sync_queue_name, task_data)
            if not sync_result:
                return False

            return True

        except Exception as e:
            LogUtils.error(f"同步过程出错: {str(e)}")
            return False

    def _query_main_(self, start_date: str, end_date: str, db: Session) -> List[dict]:
        """查询符合条件的行政案件编号（按案件编号分组，取最新入库记录，所谓批准时间实际在库里是修改时间）"""
        sql = text("""
                SELECT * FROM (
                    SELECT 
                        ajbh,
                        djdw,
                        djdwmc,
                        djrxm,
                        jdjg,
                        mc,
                        xgsj,
                        pzsj,
                        ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY xgsj DESC) AS rn
                    FROM ods_aj_flws_dxglb 
                    WHERE xgsj >= :start_date 
                    AND xgsj <= :end_date
                    AND jdjg in ('220801', '141600', '240500')
                  
                ) sub
                WHERE rn = 1
            """)
        params = {
            'start_date': start_date,
            'end_date': end_date,

        }
        return [row._asdict() for row in db.execute(sql, params).fetchall()]

    def _query_other_(self, main_data: List[dict], db: Session) -> List[dict]:
        # main_data循环 根据 ajbh 查询其他案件编号
        for row in main_data:
            ajbh = row['ajbh']
            # 案件信息补充
            sql = text(
                "SELECT ajmc FROM ods_aj_jbxx WHERE ajbh = :ajbh  order by fh_rksj desc limit 1")
            params = {
                "ajbh": ajbh
            }
            ajs = [row._asdict() for row in db.execute(sql, params).fetchall()]
            if not ajs:
                row['ajmc'] = ""
            else:
                row['ajmc'] = ajs[0].get("ajmc", "")

        return main_data

    def _query_finance_(self, main_data: List[dict], db: Session):
        finance_data = dict()
        for row in main_data:
            ajbh = row['ajbh']
            params = {"ajbh": ajbh}
            current_status = '扣押'

            # 查询登记入库记录
            sacw_djrkjl_dict = {}
            sacw_djrkjl_sql = text("""
                        SELECT * FROM (
                            SELECT 
                                ajbh,
                                ajmc,
                                cwbh,
                                cwmc,
                                djsj,
                                cwsl,
                                gl_fdqx,
                                gl_bgdqsj,
                                moder_time,
                                ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY fh_rksj DESC) AS rn
                            FROM ods_sacw_djrkjl
                            WHERE ajbh = :ajbh
                        ) sub
                        WHERE rn = 1
                    """)
            sacw_djrkjl_results = [row._asdict() for row in db.execute(sacw_djrkjl_sql, params).fetchall()]
            if sacw_djrkjl_results:
                sacw_djrkjl_dict = sacw_djrkjl_results[0]
                current_status = '入库'

            # 查询保管延期记录
            sacw_bgyq_dict = {}
            sacw_bgyq_sql = text("""
                        SELECT * FROM (
                            SELECT 
                                ajbh,
                                ajmc,
                                cwbh,
                                cwmc,
                                czsj,
                                ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY fh_rksj DESC) AS rn
                            FROM ods_sacw_bgyq
                            WHERE ajbh = :ajbh
                        ) sub
                        WHERE rn = 1
                    """)
            sacw_bgyq_results = [row._asdict() for row in db.execute(sacw_bgyq_sql, params).fetchall()]
            if sacw_bgyq_results:
                sacw_bgyq_dict = sacw_bgyq_results[0]
                current_status = '延期'

            # 查询处理出库记录
            sacw_clck_dict = {}
            sacw_clck_sql = text("""
                        SELECT * FROM (
                            SELECT 
                                ajbh,
                                ajmc,
                                cwbh,
                                cwmc,
                                czsj,
                                ROW_NUMBER() OVER (PARTITION BY ajbh ORDER BY fh_rksj DESC) AS rn
                            FROM ods_sacw_clck
                            WHERE ajbh = :ajbh
                        ) sub
                        WHERE rn = 1
                    """)
            sacw_clck_results = [row._asdict() for row in db.execute(sacw_clck_sql, params).fetchall()]
            if sacw_clck_results:
                sacw_clck_dict = sacw_clck_results[0]
                current_status = '出库'

            finance_data[ajbh] = [sacw_djrkjl_dict, sacw_bgyq_dict, sacw_clck_dict, current_status]

        return finance_data


_instance = None


async def get_scene_6_service() -> Scene6Service:
    global _instance
    if not _instance:
        _instance = Scene6Service()
        await _instance.async_init()
    return _instance
