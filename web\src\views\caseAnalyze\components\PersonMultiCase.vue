<template>
  <div class="person-multi-case">
    <div class="search-section">
      <el-form
        :inline="true"
        :model="searchForm"
        :rules="rules"
        ref="searchForm"
        class="common-search search-form"
        label-width="120px"
      >
        <div class="search-left">
          <el-form-item label="身份证号" prop="id_card">
            <el-input v-model="searchForm.id_card" placeholder="输入身份证号"></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="searchForm.name" placeholder="输入姓名"></el-input>
          </el-form-item>
        </div>
        <div class="search-right">
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="sub-total">共找到{{ total }}组一人多次</div>
    <div class="case-list">
      <div v-for="item in personList" :key="item.id_card" class="case-card">
        <div class="card-header">
          <span class="card-title">{{ item.name }} 涉案{{ item.cases.length }}起</span>
          <span class="card-id">身份证: {{ item.id_card }}</span>
        </div>
        <div class="card-body">
          <div class="case-table table-container">
            <el-table :data="item.cases" style="width: 100%" size="small">
              <el-table-column prop="case" label="处警编号" width="250" />
              <el-table-column prop="case_type" label="类别" width="180" />
              <el-table-column prop="location" label="案件地点" />
              <el-table-column prop="police_time" label="报警时间" width="200">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.police_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="role" label="角色类型" />
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getClusterEntities } from "@/api/caseAnalyze";

export default {
  name: "PersonMultiCase",
  data() {
    return {
      searchForm: {
        id_card: "",
        name: "",
      },
      rules: {
        id_card: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
      },
      personList: [],
      total: 0,
      page: 1,
      pageSize: 10,
    };
  },
  methods: {
    async fetchPersonList() {
      try {
        const params = {
          ...this.searchForm,
          page: this.page,
          page_size: this.pageSize,
        };

        const response = await getClusterEntities(params);
        if (response.code === 200) {
          const { items, total } = response.data;
          this.total = total;

          // 按身份证号分组处理数据
          const groupedData = {};
          items.forEach(item => {
            if (!groupedData[item.id_card]) {
              groupedData[item.id_card] = {
                id_card: item.id_card,
                name: item.name,
                cases: [],
              };
            }
            groupedData[item.id_card].cases.push({
              case: item.case,
              police_time: item.police_time,
              role: item.role,
            });
          });

          this.personList = Object.values(groupedData);
        }
      } catch (error) {
        console.error("获取一人多次数据失败:", error);
        this.$message.error("获取数据失败");
      }
    },
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "";
      const date = new Date(dateTimeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchPersonList();
    },

    handleCurrentChange(val) {
      this.page = val;
      this.fetchPersonList();
    },

    handleSearch() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.page = 1;
          this.fetchPersonList();
        } else {
          this.$message.warning("请填写必填项");
          return false;
        }
      });
    },

    resetSearch() {
      this.$refs.searchForm.resetFields();
      this.page = 1;
      this.fetchPersonList();
    },
  },
  created() {
    this.fetchPersonList();
  },
};
</script>

<style lang="less" scoped>
.person-multi-case {
  position: relative;
  height: calc(100vh - 200px);
  min-height: 500px;
  display: flex;
  flex-direction: column;

  .search-section {
    padding: 24px 32px 8px 32px;
    border-radius: 8px;
    margin-bottom: 18px;
    .el-form-item {
      margin-right: 24px;
      .el-input,
      .el-select {
        color: #fff;
      }
    }
  }

  .sub-total {
    color: #bfc8e2;
    font-size: 14px;
    margin-bottom: 20px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    display: inline-block;
  }

  .case-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px;
    margin-bottom: 20px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }
  }

  .case-card {
    background: #22304a;
    border-radius: 8px;
    margin-bottom: 24px;
    padding: 18px 24px 12px 24px;
    .card-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
      padding: 16px 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
      }

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        flex: 1;
        min-width: 300px;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: "";
          display: inline-block;
          width: 4px;
          height: 16px;
          background: #409eff;
          border-radius: 2px;
        }
      }

      .card-id {
        color: #bfc8e2;
        font-size: 14px;
        padding: 4px 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
      }
    }
  }

  .pagination-container {
    padding: 0 20px;
    text-align: right;
    background: #18233a;
  }
}
</style>
