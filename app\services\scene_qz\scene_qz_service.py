import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List

import aiohttp
from sqlalchemy.orm import Session
from sqlalchemy.sql.expression import case
from sqlalchemy import or_, and_, text, func, distinct
from app.db.decorators import transactional
from app.log.log_utils import LogUtils
from app.models.qz_case_feature import QzCaseFeature
from app.models.police_record import PoliceRecord
from collections import defaultdict

from app.config import settings
from app.core.task_queue import TaskQueueManager


class SceneQzService:

    def __init__(self):
        super().__init__()
        # 注册场景5分析队列
        self.queue_name = "scene_8_analysis"

        # 异步分析队列
        self.queue_manager = TaskQueueManager()

    async def async_init(self):
        # 异步初始化代码
        await self.queue_manager.register_queue(
            queue_name=self.queue_name,
            processor=self._sync,
            max_size=1000,  # 队列容量
            concurrency=5  # 最大并发数
        )
    @staticmethod
    @transactional
    async def search_cases_by_entity(id_card: str, name: str, page: int = 1, page_size: int = 10, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"根据实体查询案件，身份证: {id_card}, 姓名: {name}, 页码: {page}, 每页数量: {page_size}")
        try:
            # 使用单个优化的SQL查询，避免临时表问题
            LogUtils.info(f"执行优化的SQL查询：身份证={id_card}, 姓名={name}")

            # 计算分页参数
            offset = (page - 1) * page_size

            if id_card:
                # 有身份证的查询逻辑
                main_query_sql = """
                WITH pivot_data AS (
                    SELECT
                        case_id,
                        entity_id,
                        MAX(CASE WHEN feature_type = 'ID_CARD' THEN feature_value END) AS ID_CARD,
                        MAX(CASE WHEN feature_type = 'ENTITY_NAME' THEN feature_value END) AS ENTITY_NAME,
                        MAX(CASE WHEN feature_type = 'ENTITY_TYPE' THEN feature_value END) AS ENTITY_TYPE,
                        MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END) AS CASE_TYPE,
                        MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END) AS LOCATION
                    FROM qz_case_feature
                    GROUP BY case_id, entity_id
                ),
                target_entities AS (
                    SELECT DISTINCT entity_id, case_id
                    FROM pivot_data
                    WHERE (ID_CARD = :id_card AND ENTITY_NAME = :name)
                       OR (ENTITY_NAME = :name AND ID_CARD IS NULL
                           AND NOT EXISTS (
                               SELECT 1 FROM pivot_data p2
                               WHERE p2.ENTITY_NAME = :name AND p2.ID_CARD IS NOT NULL
                           ))
                ),
                entities_with_multiple_cases AS (
                    SELECT entity_id
                    FROM target_entities
                    GROUP BY entity_id
                    HAVING COUNT(DISTINCT case_id) >= 2
                ),
                entity_clusters AS (
                    SELECT
                        COALESCE(p.ID_CARD, CONCAT('NO_ID_CARD_', p.ENTITY_NAME)) as cluster_key,
                        p.case_id,
                        p.entity_id,
                        p.ID_CARD,
                        p.ENTITY_NAME,
                        p.ENTITY_TYPE,
                        p.CASE_TYPE,
                        p.LOCATION,
                        pr.police_time
                    FROM pivot_data p
                    JOIN entities_with_multiple_cases e ON p.entity_id = e.entity_id
                    LEFT JOIN police_records pr ON p.case_id = pr.police_number
                    WHERE pr.source = 0
                ),
                cluster_summary AS (
                    SELECT
                        cluster_key,
                        MAX(CASE WHEN ID_CARD IS NOT NULL THEN ID_CARD END) as representative_id_card,
                        MAX(ENTITY_NAME) as representative_name,
                        COUNT(DISTINCT case_id) as case_count,
                        GROUP_CONCAT(DISTINCT
                            CONCAT(case_id, '|', COALESCE(ENTITY_TYPE, 'null'), '|',
                                   COALESCE(DATE_FORMAT(police_time, '%Y-%m-%d %H:%i:%s'), ''), '|',
                                   COALESCE(CASE_TYPE, ''), '|',
                                   COALESCE(LOCATION, ''))
                            ORDER BY police_time DESC
                            SEPARATOR ';;'
                        ) as cases_info
                    FROM entity_clusters
                    GROUP BY cluster_key
                    HAVING case_count >= 2
                )
                SELECT * FROM cluster_summary
                ORDER BY case_count DESC
                LIMIT :limit OFFSET :offset
                """
                params = {
                    'id_card': id_card,
                    'name': name,
                    'limit': page_size,
                    'offset': offset
                }
            else:
                # 仅姓名的查询逻辑
                main_query_sql = """
                WITH pivot_data AS (
                    SELECT
                        case_id,
                        entity_id,
                        MAX(CASE WHEN feature_type = 'ID_CARD' THEN feature_value END) AS ID_CARD,
                        MAX(CASE WHEN feature_type = 'ENTITY_NAME' THEN feature_value END) AS ENTITY_NAME,
                        MAX(CASE WHEN feature_type = 'ENTITY_TYPE' THEN feature_value END) AS ENTITY_TYPE,
                        MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END) AS CASE_TYPE,
                        MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END) AS LOCATION
                    FROM qz_case_feature
                    GROUP BY case_id, entity_id
                ),
                target_entities AS (
                    SELECT DISTINCT entity_id, case_id
                    FROM pivot_data
                    WHERE ENTITY_NAME = :name
                ),
                entities_with_multiple_cases AS (
                    SELECT entity_id
                    FROM target_entities
                    GROUP BY entity_id
                    HAVING COUNT(DISTINCT case_id) >= 2
                ),
                entity_clusters AS (
                    SELECT
                        COALESCE(p.ID_CARD, CONCAT('NO_ID_CARD_', p.ENTITY_NAME)) as cluster_key,
                        p.case_id,
                        p.entity_id,
                        p.ID_CARD,
                        p.ENTITY_NAME,
                        p.ENTITY_TYPE,
                        p.CASE_TYPE,
                        p.LOCATION,
                        pr.police_time
                    FROM pivot_data p
                    JOIN entities_with_multiple_cases e ON p.entity_id = e.entity_id
                    LEFT JOIN police_records pr ON p.case_id = pr.police_number
                    WHERE pr.source = 0
                ),
                cluster_summary AS (
                    SELECT
                        cluster_key,
                        MAX(CASE WHEN ID_CARD IS NOT NULL THEN ID_CARD END) as representative_id_card,
                        MAX(ENTITY_NAME) as representative_name,
                        COUNT(DISTINCT case_id) as case_count,
                        GROUP_CONCAT(DISTINCT
                            CONCAT(case_id, '|', COALESCE(ENTITY_TYPE, 'null'), '|',
                                   COALESCE(DATE_FORMAT(police_time, '%Y-%m-%d %H:%i:%s'), ''), '|',
                                   COALESCE(CASE_TYPE, ''), '|',
                                   COALESCE(LOCATION, ''))
                            ORDER BY police_time DESC
                            SEPARATOR ';;'
                        ) as cases_info
                    FROM entity_clusters
                    GROUP BY cluster_key
                    HAVING case_count >= 2
                )
                SELECT * FROM cluster_summary
                ORDER BY case_count DESC
                LIMIT :limit OFFSET :offset
                """
                params = {
                    'name': name,
                    'limit': page_size,
                    'offset': offset
                }

            LogUtils.info("执行主查询SQL")
            result = db.execute(text(main_query_sql), params)
            clusters = result.fetchall()

            # 如果没有结果，返回空
            if not clusters:
                LogUtils.info("未找到符合条件的实体聚类")
                return {
                    "total": 0,
                    "items": [],
                    "page": page,
                    "page_size": page_size
                }

            LogUtils.info(f"找到{len(clusters)}个实体聚类")

            # 解析查询结果并构建返回数据
            all_clusters = []
            for cluster in clusters:
                cluster_key = cluster[0]
                representative_id_card = cluster[1]
                representative_name = cluster[2]
                case_count = cluster[3]
                cases_info = cluster[4]

                # 解析案件信息字符串
                cases_list = []
                if cases_info:
                    case_items = cases_info.split(';;')
                    for case_item in case_items:
                        parts = case_item.split('|')
                        if len(parts) >= 5:
                            case_id = parts[0]
                            entity_type = parts[1] if parts[1] != 'null' else None
                            police_time = parts[2] if parts[2] else None
                            case_type = parts[3] if parts[3] else None
                            location = parts[4] if parts[4] else None

                            cases_list.append({
                                "role": [entity_type] if entity_type else [],
                                "case": case_id,
                                "police_time": police_time,
                                "case_type": case_type,
                                "location": location
                            })

                # 确保至少有两个案件
                if len(cases_list) >= 2:
                    all_clusters.append({
                        "name": representative_name,
                        "id_card": representative_id_card,
                        "cases": cases_list
                    })
                    LogUtils.info(f"添加聚类结果：{representative_name}({representative_id_card})，{len(cases_list)}个案件")

            LogUtils.info(f"构建聚类结果完成，共{len(all_clusters)}个符合条件的实体聚类")

            # 获取总数（使用相同的查询逻辑但不分页）
            if id_card:
                count_sql = """
                WITH pivot_data AS (
                    SELECT
                        case_id,
                        entity_id,
                        MAX(CASE WHEN feature_type = 'ID_CARD' THEN feature_value END) AS ID_CARD,
                        MAX(CASE WHEN feature_type = 'ENTITY_NAME' THEN feature_value END) AS ENTITY_NAME,
                        MAX(CASE WHEN feature_type = 'ENTITY_TYPE' THEN feature_value END) AS ENTITY_TYPE,
                        MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END) AS CASE_TYPE,
                        MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END) AS LOCATION
                    FROM qz_case_feature
                    GROUP BY case_id, entity_id
                ),
                target_entities AS (
                    SELECT DISTINCT entity_id, case_id
                    FROM pivot_data
                    WHERE (ID_CARD = :id_card AND ENTITY_NAME = :name)
                       OR (ENTITY_NAME = :name AND ID_CARD IS NULL
                           AND NOT EXISTS (
                               SELECT 1 FROM pivot_data p2
                               WHERE p2.ENTITY_NAME = :name AND p2.ID_CARD IS NOT NULL
                           ))
                ),
                entities_with_multiple_cases AS (
                    SELECT entity_id
                    FROM target_entities
                    GROUP BY entity_id
                    HAVING COUNT(DISTINCT case_id) >= 2
                ),
                entity_clusters AS (
                    SELECT
                        COALESCE(p.ID_CARD, CONCAT('NO_ID_CARD_', p.ENTITY_NAME)) as cluster_key
                    FROM pivot_data p
                    JOIN entities_with_multiple_cases e ON p.entity_id = e.entity_id
                    LEFT JOIN police_records pr ON p.case_id = pr.police_number
                    WHERE pr.source = 0
                    GROUP BY cluster_key
                    HAVING COUNT(DISTINCT p.case_id) >= 2
                )
                SELECT COUNT(*) as total FROM entity_clusters
                """
                count_params = {'id_card': id_card, 'name': name}
            else:
                count_sql = """
                WITH pivot_data AS (
                    SELECT
                        case_id,
                        entity_id,
                        MAX(CASE WHEN feature_type = 'ID_CARD' THEN feature_value END) AS ID_CARD,
                        MAX(CASE WHEN feature_type = 'ENTITY_NAME' THEN feature_value END) AS ENTITY_NAME,
                        MAX(CASE WHEN feature_type = 'ENTITY_TYPE' THEN feature_value END) AS ENTITY_TYPE,
                        MAX(CASE WHEN feature_type = 'CASE_TYPE' THEN feature_value END) AS CASE_TYPE,
                        MAX(CASE WHEN feature_type = 'LOCATION' THEN feature_value END) AS LOCATION
                    FROM qz_case_feature
                    GROUP BY case_id, entity_id
                ),
                target_entities AS (
                    SELECT DISTINCT entity_id, case_id
                    FROM pivot_data
                    WHERE ENTITY_NAME = :name
                ),
                entities_with_multiple_cases AS (
                    SELECT entity_id
                    FROM target_entities
                    GROUP BY entity_id
                    HAVING COUNT(DISTINCT case_id) >= 2
                ),
                entity_clusters AS (
                    SELECT
                        COALESCE(p.ID_CARD, CONCAT('NO_ID_CARD_', p.ENTITY_NAME)) as cluster_key
                    FROM pivot_data p
                    JOIN entities_with_multiple_cases e ON p.entity_id = e.entity_id
                    LEFT JOIN police_records pr ON p.case_id = pr.police_number
                    WHERE pr.source = 0
                    GROUP BY cluster_key
                    HAVING COUNT(DISTINCT p.case_id) >= 2
                )
                SELECT COUNT(*) as total FROM entity_clusters
                """
                count_params = {'name': name}

            count_result = db.execute(text(count_sql), count_params)
            total_count = count_result.scalar() or 0

            LogUtils.info(f"查询完成，返回第{page}页，共{len(all_clusters)}条记录，总计{total_count}条")
            return {
                "total": total_count,
                "items": all_clusters,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"根据实体查询案件时出错: {str(e)}")
            raise Exception(f"根据实体查询案件时出错: {str(e)}")

    @staticmethod
    @transactional
    async def cluster_entities(page: int = 1, page_size: int = 10, start_time: Optional[str] = None, end_time: Optional[str] = None, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"开始进行人员实体聚类，页码: {page}, 每页数量: {page_size}, 开始时间: {start_time}, 结束时间: {end_time}")
        try:
            # 时间过滤逻辑：将字符串时间转换为 datetime 对象
            query_filters = [PoliceRecord.source == 0]
            if start_time:
                try:
                    start_time_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    query_filters.append(PoliceRecord.police_time >= start_time_dt)
                except ValueError as e:
                    LogUtils.error(f"开始时间格式错误: {start_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"开始时间格式错误: {start_time}")
            if end_time:
                try:
                    end_time_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    query_filters.append(PoliceRecord.police_time <= end_time_dt)
                except ValueError as e:
                    LogUtils.error(f"结束时间格式错误: {end_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"结束时间格式错误: {end_time}")

            # 子查询：筛选出至少出现在两个案件中的实体
            subquery = (
                db.query(QzCaseFeature.entity_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .filter(*query_filters)
                .filter(QzCaseFeature.feature_type.in_(["ID_CARD", "ENTITY_NAME", "ENTITY_TYPE"]))
                .group_by(QzCaseFeature.entity_id)
                .having(func.count(distinct(QzCaseFeature.case_id)) >= 2)
                .subquery()
            )

            # 查询所有符合条件的实体特征及其对应的 police_time
            all_entity_features = (
                db.query(QzCaseFeature, PoliceRecord.police_time)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .filter(*query_filters)
                .filter(QzCaseFeature.feature_type.in_(["ID_CARD", "ENTITY_NAME", "ENTITY_TYPE"]))
                .filter(QzCaseFeature.entity_id.in_(subquery))
                .order_by(QzCaseFeature.case_id, QzCaseFeature.entity_id)
                .all()
            )

            LogUtils.info(f"cluster_entities找到{len(all_entity_features)}条实体特征记录，涉及{len(set(f.case_id for f, _ in all_entity_features))}个案件")

            # 查询案件的 case_type 和 location（entity_id=0）
            case_ids = {feature.case_id for feature, _ in all_entity_features}
            case_details = db.query(
                QzCaseFeature.case_id,
                func.max(
                    case(
                        (QzCaseFeature.feature_type == 'CASE_TYPE', QzCaseFeature.feature_value),
                        else_=None
                    )
                ).label('case_type'),
                func.max(
                    case(
                        (QzCaseFeature.feature_type == 'LOCATION', QzCaseFeature.feature_value),
                        else_=None
                    )
                ).label('location')
            ).\
                filter(
                    and_(
                        QzCaseFeature.case_id.in_(case_ids),
                        QzCaseFeature.entity_id == 0,
                        QzCaseFeature.feature_type.in_(['CASE_TYPE', 'LOCATION'])
                    )
                ).group_by(QzCaseFeature.case_id).subquery()

            case_info = db.query(case_details).all()
            case_info_dict = {c.case_id: c for c in case_info}

            # 数据结构初始化
            entities_data = defaultdict(
                lambda: {"id_card": None, "name": None, "cases": defaultdict(lambda: {"roles": [], "police_time": None})}
            )
            for feature, police_time in all_entity_features:
                entity_key = (feature.case_id, feature.entity_id)
                if feature.feature_type == "ID_CARD":
                    entities_data[entity_key]["id_card"] = feature.feature_value
                elif feature.feature_type == "ENTITY_NAME":
                    entities_data[entity_key]["name"] = feature.feature_value
                elif feature.feature_type == "ENTITY_TYPE":
                    entities_data[entity_key]["cases"][feature.case_id]["roles"].append(feature.feature_value)
                    entities_data[entity_key]["cases"][feature.case_id]["police_time"] = police_time

            # 按身份证号和姓名分组
            grouped_entities = defaultdict(list)
            for entity_key, data in entities_data.items():
                if data["id_card"]:
                    grouped_entities[("ID_CARD", data["id_card"])].append(entity_key)

            for entity_key, data in entities_data.items():
                is_clustered_by_id_card = False
                if data["id_card"]:
                    for id_card_key, entity_keys_in_group in grouped_entities.items():
                        if id_card_key[0] == "ID_CARD" and entity_key in entity_keys_in_group:
                            is_clustered_by_id_card = True
                            break

                if not is_clustered_by_id_card and data["name"]:
                    name_group_key = ("NAME", data["name"])
                    is_in_name_group = False
                    if name_group_key in grouped_entities:
                        if entity_key in grouped_entities[name_group_key]:
                            is_in_name_group = True

                    if not is_in_name_group:
                        has_other_entity_with_id_card = False
                        for other_entity_key, other_data in entities_data.items():
                            if (
                                other_entity_key != entity_key
                                and other_data["name"] == data["name"]
                                and other_data["id_card"]
                            ):
                                has_other_entity_with_id_card = True
                                break

                        if not has_other_entity_with_id_card:
                            grouped_entities[name_group_key].append(entity_key)
                        else:
                            grouped_entities[("ID_CARD", other_data["id_card"])].append(entity_key)

            # 构建聚类结果
            all_clusters = []
            for group_key, entity_keys in grouped_entities.items():
                cluster_type, cluster_value = group_key
                cluster_entities_details = []
                for entity_key in entity_keys:
                    details = entities_data[entity_key]
                    cases_list = []
                    for case_id, case_info in details["cases"].items():
                        case_data = case_info_dict.get(case_id)
                        cases_list.append(
                            {
                                "role": list(set(case_info["roles"])),
                                "case": case_id,
                                "police_time": case_info["police_time"],
                                "case_type": case_data.case_type if case_data else None,
                                "location": case_data.location if case_data else None
                            }
                        )
                    cluster_entities_details.append(
                        {
                            "name": details["name"],
                            "id_card": details["id_card"],
                            "cases": cases_list,
                        }
                    )

                if cluster_entities_details:
                    representative_entity = cluster_entities_details[0]
                    all_cases = defaultdict(lambda: {"roles": [], "police_time": None})
                    for entity_details in cluster_entities_details:
                        for case_info in entity_details["cases"]:
                            all_cases[case_info["case"]]["roles"].extend(case_info["role"])
                            all_cases[case_info["case"]]["police_time"] = case_info["police_time"]

                    final_cases_list = []
                    for case_id, case_info in all_cases.items():
                        case_data = case_info_dict.get(case_id)
                        final_cases_list.append(
                            {
                                "role": list(set(case_info["roles"])),
                                "case": case_id,
                                "police_time": case_info["police_time"],
                                "case_type": case_data.case_type if case_data else None,
                                "location": case_data.location if case_data else None
                            }
                        )

                    if len(final_cases_list) >= 2:
                        all_clusters.append(
                            {
                                "name": representative_entity["name"],
                                "id_card": representative_entity["id_card"],
                                "cases": final_cases_list,
                            }
                        )

            LogUtils.info(f"cluster_entities构建聚类结果完成，共{len(all_clusters)}个实体聚类")

            # 分页
            offset = (page - 1) * page_size
            paginated_clusters = all_clusters[offset : offset + page_size]

            LogUtils.info(f"cluster_entities查询完成，返回第{page}页，共{len(paginated_clusters)}条记录")
            return {
                "total": len(all_clusters),
                "items": paginated_clusters,
                "page": page,
                "page_size": page_size,
            }

        except Exception as e:
            LogUtils.error(f"人员实体聚类时出错: {str(e)}")
            raise Exception(f"人员实体聚类时出错: {str(e)}")
    @staticmethod
    @transactional
    async def search_cases_with_shared_entities(entities: List[Dict[str, Optional[str]]], case_type: str, page: int = 1, page_size: int = 10, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"根据多个实体和案件类型查询案件，实体: {[(e.get('id_card'), e.get('name')) for e in entities]}, 案件类型: {case_type}, 页码: {page}, 每页数量: {page_size}")
        try:
            target_entity_ids_set = set()
            for entity_input in entities:
                name_condition_on_feature = and_(
                    QzCaseFeature.feature_type == 'ENTITY_NAME',
                    QzCaseFeature.feature_value == entity_input.get("name")
                )

                current_entity_matches_for_input = set()

                if entity_input.get("id_card"):
                    entities_with_this_id_card_q = db.query(QzCaseFeature.entity_id).filter(
                        and_(
                            QzCaseFeature.feature_type == 'ID_CARD',
                            QzCaseFeature.feature_value == entity_input.get("id_card")
                        )
                    ).distinct()

                    ids_of_entities_with_id_card_in_db = {eid for (eid,) in entities_with_this_id_card_q.all()}

                    if ids_of_entities_with_id_card_in_db:
                        precise_condition = and_(
                            name_condition_on_feature,
                            QzCaseFeature.entity_id.in_(ids_of_entities_with_id_card_in_db)
                        )
                        query_result = db.query(QzCaseFeature.case_id, QzCaseFeature.entity_id).\
                            filter(precise_condition).distinct().all()
                        current_entity_matches_for_input.update((case_id, entity_id) for case_id, entity_id in query_result)
                    else:
                        LogUtils.info(f"ID card {entity_input.get('id_card')} for {entity_input.get('name')} not found in DB. Falling back to name-only search for this entity to build target_entity_ids_set.")
                        query_result_name_only = db.query(QzCaseFeature.case_id, QzCaseFeature.entity_id).\
                            filter(name_condition_on_feature).distinct().all()
                        current_entity_matches_for_input.update((case_id, entity_id) for case_id, entity_id in query_result_name_only)
                else:
                    query_result_name_only = db.query(QzCaseFeature.case_id, QzCaseFeature.entity_id).\
                        filter(name_condition_on_feature).distinct().all()
                    current_entity_matches_for_input.update((case_id, entity_id) for case_id, entity_id in query_result_name_only)

                target_entity_ids_set.update(current_entity_matches_for_input)

            if not target_entity_ids_set:
                LogUtils.info("未找到匹配的目标实体")
                return { "total": 0, "items": [], "page": page, "page_size": page_size }

            case_ids_from_target_entities = list(set(cid for cid, _ in target_entity_ids_set)) # Distinct case_ids
            LogUtils.info(f"找到{len(target_entity_ids_set)}个目标实体，涉及{len(case_ids_from_target_entities)}个案件")

            target_type_cases_subquery = db.query(QzCaseFeature.case_id).\
                filter(
                    and_(
                        QzCaseFeature.feature_type == 'CASE_TYPE',
                        QzCaseFeature.feature_value == case_type,
                        QzCaseFeature.case_id.in_(case_ids_from_target_entities)
                    )
                ).distinct().subquery()

            # Check if any cases match the type after initial entity filtering
            if not db.query(target_type_cases_subquery.c.case_id).first():
                LogUtils.info(f"未找到类型为'{case_type}'的案件")
                return { "total": 0, "items": [], "page": page, "page_size": page_size }

            case_to_entity_names_map = defaultdict(set)
            for entity_input_detail in entities:
                entity_name_filter_for_count = and_(
                    QzCaseFeature.feature_type == 'ENTITY_NAME',
                    QzCaseFeature.feature_value == entity_input_detail.get("name"),
                    QzCaseFeature.case_id.in_(target_type_cases_subquery)
                )

                entity_related_cases_query = db.query(QzCaseFeature.case_id).filter(entity_name_filter_for_count)

                if entity_input_detail.get("id_card"):
                    id_card_exists_for_any_entity_in_db = db.query(QzCaseFeature.entity_id).filter(
                        and_(
                            QzCaseFeature.feature_type == 'ID_CARD',
                            QzCaseFeature.feature_value == entity_input_detail.get("id_card")
                        )
                    ).limit(1).scalar()

                    if id_card_exists_for_any_entity_in_db is not None:
                        entity_related_cases_query = entity_related_cases_query.filter(
                            QzCaseFeature.entity_id.in_(
                                db.query(QzCaseFeature.entity_id).filter(
                                    and_(
                                        QzCaseFeature.feature_type == 'ID_CARD',
                                        QzCaseFeature.feature_value == entity_input_detail.get("id_card")
                                    )
                                ).subquery()
                            )
                        )
                    else:
                        LogUtils.info(f"ID card {entity_input_detail.get('id_card')} for {entity_input_detail.get('name')} not found in DB. Not using for filtering in case_to_entity_names_map.")

                case_ids_for_this_entity = {cid for (cid,) in entity_related_cases_query.distinct().all()}
                for cid_val in case_ids_for_this_entity:
                    case_to_entity_names_map[cid_val].add(entity_input_detail.get("name"))

            valid_case_ids_set = {cid for cid, names in case_to_entity_names_map.items() if len(names) >= 2}

            if not valid_case_ids_set:
                LogUtils.info("未找到包含多个目标实体的案件")
                return { "total": 0, "items": [], "page": page, "page_size": page_size }

            LogUtils.info(f"找到{len(valid_case_ids_set)}个包含多个目标实体的案件")

            total_valid_cases = len(valid_case_ids_set)
            offset = (page - 1) * page_size
            paginated_case_ids_list = list(valid_case_ids_set)[offset:offset + total_valid_cases] # Corrected slicing for all matching, then paginate display

            if not paginated_case_ids_list:
                 return { "total": total_valid_cases, "items": [], "page": page, "page_size": page_size }

            # Fetch details for the cases that are on the current page after all filtering
            current_page_case_ids = paginated_case_ids_list[0:page_size] # Apply page_size for current query
            if not current_page_case_ids:
                 return { "total": total_valid_cases, "items": [], "page": page, "page_size": page_size }


            case_details_query_results = db.query(
                QzCaseFeature.case_id,
                QzCaseFeature.feature_value.label("case_type_from_feature"),
                PoliceRecord.police_time
            ).\
                join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number).\
                filter(
                    and_(
                        QzCaseFeature.case_id.in_(current_page_case_ids), # Use current_page_case_ids
                        QzCaseFeature.feature_type == 'CASE_TYPE',
                        PoliceRecord.source == 0
                    )
                ).distinct().all()

            case_ids_in_details = {cd_row.case_id for cd_row in case_details_query_results}

            if not case_ids_in_details:
                 return { "total": total_valid_cases, "items": [], "page": page, "page_size": page_size }

            all_related_entities_data = db.query(
                QzCaseFeature.case_id,
                QzCaseFeature.entity_id,
                QzCaseFeature.feature_type,
                QzCaseFeature.feature_value
            ).\
                filter(
                    and_(
                        QzCaseFeature.case_id.in_(case_ids_in_details),
                        QzCaseFeature.feature_type.in_(['ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD'])
                    )
                ).all()

            entity_information_map = defaultdict(lambda: {"name": None, "role": None, "id_card": None})
            for cid, eid, f_type, f_value in all_related_entities_data:
                entity_key = (cid, eid)
                if f_type == 'ENTITY_NAME':
                    entity_information_map[entity_key]['name'] = f_value
                elif f_type == 'ENTITY_TYPE':
                    entity_information_map[entity_key]['role'] = f_value
                elif f_type == 'ID_CARD':
                    entity_information_map[entity_key]['id_card'] = f_value

            target_roles_per_case = defaultdict(list)
            other_involved_per_case = defaultdict(list)

            for (case_id_key, entity_id_key), info in entity_information_map.items():
                if info.get('name'):
                    entity_payload = {
                        "name": info['name'],
                        "id_card": info.get('id_card'),
                        "role": info.get('role')
                    }
                    if (case_id_key, entity_id_key) in target_entity_ids_set:
                        target_roles_per_case[case_id_key].append(entity_payload)
                    else:
                        other_involved_per_case[case_id_key].append({
                            "id": entity_id_key,
                            "name": info['name'],
                            "role": info.get('role'),
                            "id_card": info.get('id_card')
                        })

            output_items = []
            for detail_row in case_details_query_results:
                case_id_val = detail_row.case_id
                output_items.append({
                    "case": case_id_val,
                    "case_type": detail_row.case_type_from_feature,
                    "roles": target_roles_per_case.get(case_id_val, []),
                    "other_involved": other_involved_per_case.get(case_id_val, []),
                    "police_time": detail_row.police_time
                })

            LogUtils.info(f"search_cases_with_shared_entities查询完成，返回第{page}页，共{len(output_items)}条记录")
            return {
                "total": total_valid_cases,
                "items": output_items,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"根据多个实体和案件类型查询案件时出错: {str(e)}")
            raise Exception(f"根据多个实体和案件类型查询案件时出错: {str(e)}")

    @staticmethod
    @transactional
    async def cluster_cases_with_shared_entities(page: int = 1, page_size: int = 10, start_time: Optional[str] = None, end_time: Optional[str] = None, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"开始进行具有共享实体的案件聚类，页码: {page}, 每页数量: {page_size}, 开始时间: {start_time}, 结束时间: {end_time}")
        try:
            # 时间过滤逻辑
            query_filters = [PoliceRecord.source == 0]
            if start_time:
                try:
                    start_time_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    query_filters.append(PoliceRecord.police_time >= start_time_dt)
                except ValueError as e:
                    LogUtils.error(f"开始时间格式错误: {start_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"开始时间格式错误: {start_time}")
            if end_time:
                try:
                    end_time_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    query_filters.append(PoliceRecord.police_time <= end_time_dt)
                except ValueError as e:
                    LogUtils.error(f"结束时间格式错误: {end_time}, 应为 'YYYY-MM-DD HH:MM:SS'")
                    raise ValueError(f"结束时间格式错误: {end_time}")

            # 子查询：筛选出至少出现在两个案件中的实体
            entities_with_multiple_cases = (
                db.query(QzCaseFeature.entity_id, QzCaseFeature.feature_value.label('entity_name'))
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .filter(*query_filters)
                .filter(QzCaseFeature.feature_type == 'ENTITY_NAME')
                .group_by(QzCaseFeature.entity_id, QzCaseFeature.feature_value)
                .having(func.count(distinct(QzCaseFeature.case_id)) > 1)
                .subquery()
            )
            entities_list = db.query(entities_with_multiple_cases).all()
            LogUtils.info(f"找到{len(entities_list)}个出现在多个案件中的实体")

            # 获取相关案件
            entity_cases = (
                db.query(QzCaseFeature.case_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(entities_with_multiple_cases, QzCaseFeature.entity_id == entities_with_multiple_cases.c.entity_id)
                .filter(*query_filters)
                .filter(QzCaseFeature.feature_type == 'ENTITY_NAME')
                .distinct()
                .subquery()
            )

            # 筛选案件类型
            target_cases = (
                db.query(QzCaseFeature.case_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .filter(
                    and_(
                        QzCaseFeature.feature_type == 'CASE_TYPE',
                        QzCaseFeature.case_id.in_(db.query(entity_cases.c.case_id)),
                        *query_filters
                    )
                )
                .distinct()
                .subquery()
            )

            # 查询所有相关实体
            all_involved_entities = (
                db.query(
                    QzCaseFeature.case_id,
                    QzCaseFeature.entity_id,
                    QzCaseFeature.feature_type,
                    QzCaseFeature.feature_value
                )
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(target_cases, QzCaseFeature.case_id == target_cases.c.case_id)
                .filter(
                    and_(
                        QzCaseFeature.feature_type.in_(['ID_CARD', 'ENTITY_NAME']),
                        *query_filters
                    )
                )
                .distinct()
                .subquery()
            )

            shared_entities_data = db.query(
                all_involved_entities.c.case_id,
                all_involved_entities.c.entity_id,
                all_involved_entities.c.feature_type,
                all_involved_entities.c.feature_value
            ).all()

            # 构建实体映射：(name, id_card) -> [(case_id, entity_id)]
            entity_map = defaultdict(list)
            name_to_id_cards = defaultdict(set)  # name -> set(id_card)
            for case_id, entity_id, feature_type, feature_value in shared_entities_data:
                if feature_type == 'ENTITY_NAME':
                    id_card = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ID_CARD'
                        )
                    ).scalar()
                    entity_map[(feature_value, id_card)].append((case_id, entity_id))
                    if id_card:
                        name_to_id_cards[feature_value].add(id_card)

            # 合并无身份证的实体
            final_entity_map = defaultdict(set)  # (name, id_card) -> set(case_id)
            for (name, id_card), case_entities in entity_map.items():
                if id_card is None:
                    matched_id_card = next(iter(name_to_id_cards[name]), None) if name in name_to_id_cards else None
                    if matched_id_card:
                        for case_id, _ in case_entities:
                            final_entity_map[(name, matched_id_card)].add(case_id)
                    else:
                        for case_id, _ in case_entities:
                            final_entity_map[(name, None)].add(case_id)
                else:
                    for case_id, _ in case_entities:
                        final_entity_map[(name, id_card)].add(case_id)

            # 筛选具有共享实体的案件
            case_entity_count = defaultdict(set)
            for (name, id_card), case_ids in final_entity_map.items():
                for case_id in case_ids:
                    case_entity_count[case_id].add((name, id_card))

            valid_cases = {case_id for case_id, entities in case_entity_count.items() if len(entities) >= 2}
            shared_entity_keys = [
                (name, id_card) for (name, id_card), case_ids in final_entity_map.items()
                if len(case_ids & valid_cases) >= 2
            ]
            LogUtils.info(f"找到{len(valid_cases)}个包含多个共享实体的案件，{len(shared_entity_keys)}个共享实体")

            # 筛选具有共享实体的案件
            cases_with_shared_entities = (
                db.query(QzCaseFeature.case_id)
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(target_cases, QzCaseFeature.case_id == target_cases.c.case_id)
                .filter(
                    and_(
                        QzCaseFeature.feature_type == 'ENTITY_NAME',
                        QzCaseFeature.feature_value.in_([name for name, _ in shared_entity_keys]),
                        QzCaseFeature.case_id.in_(valid_cases),
                        *query_filters
                    )
                )
                .distinct()
                .subquery()
            )

            # 查询聚类案件的详细信息，包含所有实体
            clustered_cases_data = (
                db.query(
                    QzCaseFeature.case_id,
                    QzCaseFeature.entity_id,
                    QzCaseFeature.feature_type,
                    QzCaseFeature.feature_value,
                    PoliceRecord.police_time
                )
                .join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number)
                .join(cases_with_shared_entities, QzCaseFeature.case_id == cases_with_shared_entities.c.case_id)
                .filter(
                    and_(
                        QzCaseFeature.feature_type.in_(['CASE_TYPE', 'ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD']),
                        *query_filters
                    )
                )
                .order_by(QzCaseFeature.case_id, QzCaseFeature.entity_id)
                .all()
            )

            cluster_data = defaultdict(lambda: {
                "involved": defaultdict(dict),
                "cases": defaultdict(dict)
            })

            for item in clustered_cases_data:
                case_id = item.case_id
                entity_id = item.entity_id
                police_time = item.police_time

                name = None
                id_card = None
                if item.feature_type == 'ENTITY_NAME':
                    name = item.feature_value
                    id_card = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ID_CARD'
                        )
                    ).scalar()
                elif item.feature_type == 'ID_CARD':
                    name = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ENTITY_NAME'
                        )
                    ).scalar()
                    id_card = item.feature_value
                elif item.feature_type == 'ENTITY_TYPE':
                    name = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ENTITY_NAME'
                        )
                    ).scalar()
                    id_card = db.query(QzCaseFeature.feature_value).filter(
                        and_(
                            QzCaseFeature.case_id == case_id,
                            QzCaseFeature.entity_id == entity_id,
                            QzCaseFeature.feature_type == 'ID_CARD'
                        )
                    ).scalar()

                if item.feature_type == 'CASE_TYPE':
                    cluster_data[case_id]["cases"][case_id]["case_type"] = item.feature_value
                    cluster_data[case_id]["cases"][case_id]["case"] = case_id
                    cluster_data[case_id]["cases"][case_id]["police_time"] = police_time
                elif item.feature_type == 'ENTITY_NAME' and name:
                    cluster_data[case_id]["involved"][entity_id]["name"] = item.feature_value
                elif item.feature_type == 'ENTITY_TYPE' and name:
                    cluster_data[case_id]["involved"][entity_id]["role"] = item.feature_value
                elif item.feature_type == 'ID_CARD' and name:
                    cluster_data[case_id]["involved"][entity_id]["id_card"] = item.feature_value

            # 构建 clusters_by_persons_and_type
            clusters_by_persons_and_type = defaultdict(list)
            for case_id, data in cluster_data.items():
                case_type = data["cases"][case_id]["case_type"] if case_id in data["cases"] else None
                if not case_type:
                    continue

                # 构建 person_key，仅包含共享实体
                person_key = set()
                entity_id_to_key = {}
                for entity_id, person_info in data["involved"].items():
                    name = person_info.get("name")
                    id_card = person_info.get("id_card")
                    if name:
                        if id_card is None:
                            matched_id_card = next((i for n, i in shared_entity_keys if n == name and i is not None), None)
                            key = (name, matched_id_card if matched_id_card else None)
                        else:
                            key = (name, id_card)
                        if key in shared_entity_keys:
                            person_key.add(key)
                            entity_id_to_key[entity_id] = key

                if len(person_key) < 2:
                    continue

                cluster_key = (frozenset(person_key), case_type)

                if case_id in data["cases"]:
                    roles = []
                    seen_entities = set()
                    for entity_id, person_info in data["involved"].items():
                        name = person_info.get("name")
                        id_card = person_info.get("id_card")
                        role = person_info.get("role") or "null"  # 如果没有 role，设置为 "null"
                        if name and entity_id_to_key.get(entity_id) in person_key:
                            original_id_card = db.query(QzCaseFeature.feature_value).filter(
                                and_(
                                    QzCaseFeature.case_id == case_id,
                                    QzCaseFeature.entity_id == entity_id,
                                    QzCaseFeature.feature_type == 'ID_CARD'
                                )
                            ).scalar()
                            key = (name, original_id_card)
                            if key not in seen_entities:
                                seen_entities.add(key)
                                roles.append({
                                    "name": name,
                                    "id_card": original_id_card,
                                    "role": role
                                })

                    clusters_by_persons_and_type[cluster_key].append({
                        "case": data["cases"][case_id]["case"],
                        "police_time": data["cases"][case_id]["police_time"],
                        "other_involved": [],
                        "roles": roles
                    })

            # 构建 final_clusters
            final_clusters = []
            for (person_key, case_type), cases in clusters_by_persons_and_type.items():
                if len(cases) < 2:
                    continue

                involved_persons = []
                seen_entities = set()
                for name, id_card in person_key:
                    if (name, id_card) not in seen_entities:
                        seen_entities.add((name, id_card))
                        involved_persons.append({
                            "name": name,
                            "id_card": id_card
                        })

                for case in cases:
                    case_id = case["case"]
                    case_persons = set()
                    for entity_id, person_info in cluster_data[case_id]["involved"].items():
                        name = person_info.get("name")
                        id_card = person_info.get("id_card")
                        if name:
                            if id_card is None:
                                matched_id_card = next((i for n, i in shared_entity_keys if n == name and i is not None), None)
                                id_card = matched_id_card if matched_id_card else None
                            case_persons.add((name, id_card))

                    involved_person_set = set((p["name"], p["id_card"]) for p in involved_persons)

                    case["other_involved"] = [
                        {
                            "name": person_info.get("name"),
                            "id_card": person_info.get("id_card") or db.query(QzCaseFeature.feature_value).filter(
                                and_(
                                    QzCaseFeature.case_id == case_id,
                                    QzCaseFeature.entity_id == entity_id,
                                    QzCaseFeature.feature_type == 'ID_CARD'
                                )
                            ).scalar(),
                            "role": person_info.get("role") or "null"  # 如果没有 role，设置为 "null"
                        }
                        for entity_id, person_info in cluster_data[case_id]["involved"].items()
                        if person_info.get("name") and
                        (
                            person_info.get("name"),
                            person_info.get("id_card") or
                            next((i for n, i in shared_entity_keys if n == person_info.get("name") and i is not None), None)
                        ) not in involved_person_set
                    ]

                final_clusters.append({
                    "involved": involved_persons,
                    "case_type": case_type,
                    "cases": cases
                })

            total = len(final_clusters)
            offset = (page - 1) * page_size
            paginated_clusters = final_clusters[offset:offset + page_size]

            LogUtils.info(f"cluster_cases_with_shared_entities查询完成，共{total}个聚类，返回第{page}页，共{len(paginated_clusters)}条记录")
            return {
                "total": total,
                "items": paginated_clusters,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"具有共享实体的案件聚类时出错: {str(e)}")
            raise Exception(f"具有共享实体的案件聚类时出错: {str(e)}")

    @staticmethod
    @transactional
    async def search_cases_by_location(longitude: float, latitude: float, radius: float = 1000, page: int = 1, page_size: int = 10, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"根据位置查询案件，经度: {longitude}, 纬度: {latitude}, 半径: {radius}米, 页码: {page}, 每页数量: {page_size}")
        try:
            LogUtils.debug(f"输入参数: longitude={longitude}, latitude={latitude}, radius={radius}")

            spatial_condition_text = text(
                "ST_Distance_Sphere("
                "ST_SRID(ST_GeomFromText(CONCAT('POINT(', feature_value, ')')), 4326), "
                "ST_SRID(POINT(:longitude, :latitude), 4326)"
                ") <= :radius"
            )

            location_query = db.query(QzCaseFeature.case_id).\
                filter(
                    and_(
                        QzCaseFeature.feature_type == 'LOCATION',
                        QzCaseFeature.feature_value != None,
                        spatial_condition_text.params(
                            longitude=longitude,
                            latitude=latitude,
                            radius=radius
                        )
                    )
                ).\
                distinct().subquery()

            LogUtils.debug(f"SQL模板: {str(location_query)}")
            LogUtils.debug(f"绑定参数: longitude={longitude}, latitude={latitude}, radius={radius}")

            total = db.query(location_query).count()
            LogUtils.info(f"在半径{radius}米范围内找到{total}个案件")

            if total == 0:
                LogUtils.info("未找到符合位置条件的案件")
                return {
                    "total": 0,
                    "items": [],
                    "page": page,
                    "page_size": page_size
                }

            offset = (page - 1) * page_size
            paginated_case_ids = db.query(location_query).\
                offset(offset).limit(page_size).all()
            case_ids = [case_id for (case_id,) in paginated_case_ids]
            LogUtils.debug(f"分页后的案件ID: {case_ids}")

            case_details = db.query(QzCaseFeature, PoliceRecord.police_time).\
                join(PoliceRecord, QzCaseFeature.case_id == PoliceRecord.police_number).\
                filter(
                    and_(
                        QzCaseFeature.case_id.in_(case_ids),
                        QzCaseFeature.feature_type.in_(['CASE_TYPE', 'ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD', 'LOCATION']),
                        PoliceRecord.source == 0
                    )
                ).\
                order_by(QzCaseFeature.case_id, QzCaseFeature.entity_id).\
                all()

            results = []
            current_case = None
            current_result = {}
            for item, police_time in case_details:
                if item.case_id != current_case:
                    if current_result:
                        results.append(current_result)
                    current_case = item.case_id
                    current_result = {
                        "case": item.case_id,
                        "case_type": None,
                        "location": None,
                        "entities": [],
                        "police_time": police_time
                    }

                if item.feature_type == 'CASE_TYPE':
                    current_result["case_type"] = item.feature_value
                elif item.feature_type == 'LOCATION':
                    current_result["location"] = item.feature_value
                elif item.feature_type in ['ENTITY_NAME', 'ENTITY_TYPE', 'ID_CARD']:
                    entity = next((e for e in current_result["entities"] if e["entity_id"] == item.entity_id), None)
                    if not entity:
                        entity = {"entity_id": item.entity_id, "name": None, "id_card": None, "role": None}
                        current_result["entities"].append(entity)

                    if item.feature_type == 'ENTITY_NAME':
                        entity["name"] = item.feature_value
                    elif item.feature_type == 'ENTITY_TYPE':
                        entity["role"] = item.feature_value
                    elif item.feature_type == 'ID_CARD':
                        entity["id_card"] = item.feature_value

            if current_result:
                results.append(current_result)

            return {
                "total": total,
                "items": results,
                "page": page,
                "page_size": page_size
            }

        except Exception as e:
            LogUtils.error(f"根据位置查询案件时出错: {str(e)}")
            raise Exception(f"根据位置查询案件时出错: {str(e)}")

    @staticmethod
    @transactional
    async def get_periodic_stats(start_time: Optional[str] = None, end_time: Optional[str] = None, db: Session = None) -> Dict[str, Any]:
        LogUtils.info(f"获取周期性案件统计信息，开始时间: {start_time}, 结束时间: {end_time}")
        try:
            cluster_entities_result = await SceneQzService.cluster_entities(page=1, page_size=10000, start_time=start_time, end_time=end_time, db=db)
            cluster_entities_num = cluster_entities_result["total"]

            cluster_cases_result = await SceneQzService.cluster_cases_with_shared_entities(page=1, page_size=1, start_time=start_time, end_time=end_time, db=db)
            cluster_cases_num = cluster_cases_result["total"]

            #TODO: 一地多起的total返回
            cluster_locations_num = 0

            return {
                "cluster_entities_num": cluster_entities_num,
                "cluster_cases_num": cluster_cases_num,
                "cluster_locations_num": cluster_locations_num
            }
        except Exception as e:
            LogUtils.error(f"获取周期性案件统计信息时出错: {str(e)}")
            raise Exception(f"获取周期性案件统计信息时出错: {str(e)}")



    @transactional
    async def scene_qz_data_sync(self, user_id: int, start_date: str, end_date: str, db: Session = None) -> bool:
        """同步案件数据"""

        LogUtils.info(f"开始同步数据，用户ID: {user_id}, 查询时间范围: {start_date} 至 {end_date}")
        try:
            # 查询行政案件和刑事案件编号
            def convert_start_date(date_str):
                """转换为当天零点"""
                dt = datetime.strptime(date_str, "%Y%m%d")
                return dt.strftime("%Y%m%d%H%M%S")

            def convert_end_date(date_str):
                """转换为当天最后一秒"""
                dt = datetime.strptime(date_str, "%Y%m%d")
                end_time = dt.replace(hour=23, minute=59, second=59)
                return end_time.strftime("%Y%m%d%H%M%S")

            start_date = convert_start_date(start_date)
            end_date = convert_end_date(end_date)
            """查询符合条件的案件编号和id"""
            sql = text("""

                SELECT id, police_number
                FROM police_records
                WHERE alarm_time BETWEEN :start_date AND :end_date
                AND source=0
            """)
            params = {

                'start_date': start_date,
                'end_date': end_date
            }
            result = db.execute(sql, params)
            rows = result.fetchall()
            ids = [row.id for row in rows]
            police_numbers = [row.police_number for row in rows]

            # 将查询到的数据聚合到字典
            task_datas = []
            for id, police_number in zip(ids, police_numbers):
                task_data = {
                    "id": id,
                    "police_number": police_number,

                }
                task_datas.append(task_data)

            # 将所有任务添加到统一队列
            for task_data in task_datas:
                sync_result = await self.queue_manager.add_task(self.queue_name, task_data)
                if not sync_result:
                    return False

            return True

        except Exception as e:
            LogUtils.error(f"同步过程出错: {str(e)}")
            return False

    @transactional
    async def _sync(self, task_data: dict, db: Session = None):
        id = task_data['id']
        police_number = task_data["police_number"]

        try:
            # 执行处警信息提取流程
            await self._dify_process(police_number, db)

        except Exception as e:
            LogUtils.error(f"dify流程 scene_qz_record_id: {id}: {str(e)}")




    @transactional
    async def _dify_process(self, police_number: str, db: Session = None):
        # 使用aiohttp进行异步HTTP请求
        timeout = aiohttp.ClientTimeout(total=180)  # 设置30秒总超时
        async with aiohttp.ClientSession(timeout=timeout) as session:
            headers = {
                'Authorization': f'Bearer {settings.SCENE_QZ_API_KEY}',
            }

            json_data = {
                'inputs': {'ajbh': f'{police_number}'},
                'response_mode': 'blocking',
                'user': 'abc-123',
            }

            is_error = False
            try:
                async with session.post(
                        f'{settings.DIFY_API_PREIX}{settings.DIFY_WORKFLOW_ENDPOINT}',
                        headers=headers,
                        json=json_data,
                        timeout=timeout  # 为单个请求设置超时
                ) as response:
                    if response.status == 200:
                        # 获取响应数据
                        response_data = await response.json()
                        if response_data['data']['status'] != 'succeeded':
                            is_error = True
                    else:
                        is_error = True
            except asyncio.TimeoutError:
                LogUtils.error(f"任务scene_qz_record_id: {id} API请求超时")
                is_error = True

            if is_error:
                LogUtils.info(f"任务scene_qz_record_id: {id} 同步失败")

        LogUtils.info(f"任务scene_qz_record_id: {id} 处理完成")




_instance = None


async def get_scene_qz_service() -> SceneQzService:
    global _instance
    if not _instance:
        _instance = SceneQzService()
        await _instance.async_init()
    return _instance
