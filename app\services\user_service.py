
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Any, List, Dict

import jwt
from fastapi import HTT<PERSON>Exception

from passlib.context import Crypt<PERSON>ontext
from sqlalchemy import and_, text, or_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.context_handle import UserContext
from app.log.log_utils import LogUtils
from app.models.org import UserToOrg, Org, OrgToRole

from app.models.role import UserToRole, Role
from app.models.temp_org_info import  temp_org_map
from app.models.temp_org_mapping import temp_org_to_user
from app.models.user import User
from app.schemas.user_schema import UserCreateRequest, UserChangeRequest, UserBackendCreateRequest, \
    UserBackendQueryRequest, UserBackendResetPwdRequest

SECRET_KEY = "your_secret_key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60*8 # minutes

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_user(db: Session, user: UserCreateRequest):
    hashed_password = pwd_context.hash(user.password)
    db_user = User(username=user.username,
                   hashed_password=hashed_password,
                   id_card_number=user.id_card_number,
                   police_number=user.police_number,
                   police_unit=user.police_unit,
                   phone_number=user.phone_number,
                   role="POLICE",
                   is_online=1
                   )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)




    return db_user


def create_user2(db: Session, user: UserCreateRequest):
    try:
        # 创建用户基本信息
        hashed_password = pwd_context.hash(user.password)
        db_user = User(
            username=user.username,
            hashed_password=hashed_password,
            id_card_number=user.id_card_number,
            police_number=user.police_number,
            police_unit=user.police_unit,
            phone_number=user.phone_number,
            role="POLICE",
            is_online=1
        )
        db.add(db_user)
        db.flush()  # 获取用户ID

        try:
            # 添加用户-组织关系
            user_org = UserToOrg(
                user_id=db_user.id,
                org_code=user.org_code,  # 假设请求中包含org_code
                primary_flag=1  # 设置为主部门
            )
            db.add(user_org)
            #
            role= db.query(Role).filter(Role.name=='普通用户').first()
            # 添加用户-角色关系
            user_role = UserToRole(
                user_id=db_user.id,
                role_id=role.id,
            )
            db.add(user_role)

            db.commit()
            db.refresh(db_user)
            access_token = create_access_token(data={"sub": user.police_number})

            return  create_response(code=200, data={"access_token": access_token, "user": db_user})
        except SQLAlchemyError as e:
            db.rollback()
            create_response(code=500, message=f"注册失败{str(e)}")

    except Exception as e:
        db.rollback()
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=500,
            detail=f"创建用户失败: {str(e)}"
        )

def change_pwd(db: Session, change_user: UserChangeRequest):
    user = db.query(User).filter(User.police_number == change_user.police_number).first()
    try:
        user.hashed_password = pwd_context.hash(change_user.password)
        db.commit()
        db.refresh(user)
        return user
    except Exception as e:
        pass

def get_user(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()


def get_police_number(db: Session, police_number: str):
    return db.query(User).filter(User.police_number == police_number).first()

def get_id_card_number(db: Session, id_card_number: str):
    return db.query(User).filter(User.id_card_number == id_card_number).first()

def authenticate_user(db: Session, police_number: str, password: str):
    user = db.query(User).filter(User.police_number == police_number).first()
    if not user or not pwd_context.verify(password, user.hashed_password):
        return False
    return user

def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    # if expires_delta:
    #     expire = datetime.utcnow() + expires_delta
    # else:
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def logout_user(db: Session, token: str):
    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    police_number: str = payload.get("sub")
    user = get_police_number(db, police_number)
    user.is_online = 0
    db.commit()
    db.refresh(user)

def get_user_stats(db: Session):
    total_users = db.query(User).count()
    
    # 获取当前时间
    current_time = datetime.now()
    # 设置超时时间为30分钟
    timeout = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # 查询在线用户：is_online=1 且 update_time 在30分钟内
    online_users = db.query(User).filter(
        and_(
            User.is_online == 1,
            User.update_time >= current_time - timeout
        )
    ).count()
    
    return {
        "total_users": total_users,
        "online_users": online_users
    }


def get_user_to_api_premission(db: Session,userId:int) -> Any | None:
    sql = text("""
              select *  from  api_info ai where  id  in (
              select api_id from  role_org_api_permission roap  where     (roap.org_code, roap.role_id)  in  ( select uto.org_code ,utr.role_id from  user_to_org uto
              left join 
              user_to_role utr on  uto.user_id =utr.user_id  
              where  uto.user_id = :userId and primary_flag =1
              UNION
              select uto.org_code ,otr.role_id  from  user_to_org uto 
              left join  org_to_role otr  on  uto.org_code  =otr.org_code  where   uto.user_id = :userId  and primary_flag =0 and  role_id is not null ))
    """)
    result = db.execute(sql,{"userId":userId}).fetchall()
    if not result:
        return []
    api_list = []
    for row in result:
        api_dict = {
            'id': row.id,
            'name': row.name,
            'value': row.value,
            'api': row.api,
            'method': row.method
        }
        api_list.append(api_dict)

    return api_list


def get_user_to_role(db: Session,userId:int) -> Any | None:
    sql = text("""
       select DISTINCT code ,name from  role_info where id in (
           select role_id  from  user_to_role where user_id =:userId
             UNION 
         select  role_id   from   org_to_role otr  where  org_code  in  (select  org_code  from  user_to_org uto  where user_id = :userId )
          UNION SELECT id FROM role_info WHERE code = 'NORMAL_USER') 
        """)
    result = db.execute(sql,{"userId":userId}).fetchall()
    if not result:
        return []
    return [
        {
            'role_code': row.code,
            'role_name': row.name
        }
        for row in result
    ]


def get_user_to_org(db: Session,userId:int) -> Any | None:
    sql = text("""
            select org_code  from org_info where org_code in (
           select org_code  from  user_to_org where user_id =:userId and primary_flag =1)
            """)
     ## 当前用户主部门 code
    org_code = db.execute(sql,{"userId":userId}).first()
    if not org_code:
        raise HTTPException(
            status_code=403,
            detail={
                "code": 403,
                "message": f"您没有主部门",
                "data": None
            }
        )
    #主部门  映射来源  origin_org_code
    tempOrgInfo = db.query(temp_org_map).filter(temp_org_map.org_code == org_code[0]).first()
    user_org={
        'org_code':org_code[0],
        'origin_org_code':tempOrgInfo.origin_code
    }
    return [ user_org ]


def check_permission(db: Session, permission_key: str, user_id: int) -> bool:
    """检查用户是否拥有指定权限"""
    # 第一步：检查权限标识是否存在
    permission_sql = text("""
        SELECT a.* 
        FROM api_info a 
        LEFT JOIN role_org_api_permission p ON a.id = p.api_id 
        WHERE a.value = :permission_key and p.api_id is not null and  p.org_code !='-1'
    """)

    permission_result = db.execute(permission_sql, {'permission_key': permission_key}).fetchone()
    if not permission_result:
        return False
    # 第二步：检查用户是否有该权限
    user_permission_sql = text("""
        SELECT api_id 
        FROM role_org_api_permission roap 
        WHERE (roap.org_code, roap.role_id) IN (
            SELECT uto.org_code, utr.role_id 
            FROM user_to_org uto
            LEFT JOIN user_to_role utr ON uto.user_id = utr.user_id
            WHERE uto.user_id = :user_id AND primary_flag = 1

            UNION

            SELECT uto.org_code, otr.role_id
            FROM user_to_org uto
            LEFT JOIN org_to_role otr ON uto.org_code = otr.org_code
            WHERE uto.user_id = :user_id AND primary_flag = 0 AND role_id IS NOT NULL
        ) AND roap.api_id = :api_id
    """)

    user_permission = db.execute(
        user_permission_sql,
        {
            'user_id': user_id,
            'api_id': permission_result.id
        }
    ).fetchall()

    if not user_permission:
        raise HTTPException(
            status_code=403,
            detail={
                "code": 403,
                "message": f"您没有【{permission_result.name}】的操作权限",
                "data": None
            }
        )

    return True


def userBackendCreate(db: Session, userInfo: UserBackendCreateRequest):
    """后台创建用户"""
    # 1. 检查警号是否存在
    if get_police_number(db, userInfo.police_number):
        return  create_response(code=500, message="警号已存在")


    # 2. 检查身份证号是否存在
    if get_id_card_number(db, userInfo.id_card_number):
        return create_response(code=500, message="身份证号已存在")


    try:
        if not userInfo.police_unit:
            userInfo.police_unit = "POLICE"
        # 3.1 创建用户基本信息
        user =   create_user(db, userInfo)

        # 3.2 创建用户-角色关联
        if userInfo.role_ids:
            user_roles = [
                UserToRole(user_id=user.id, role_id=role_id)
                for role_id in userInfo.role_ids
            ]
            db.add_all(user_roles)

        # 3.3 创建用户-组织关联
        # 主部门
        primary_org = UserToOrg(
            user_id=user.id,
            org_code=userInfo.org_code,
            primary_flag=True
        )
        db.add(primary_org)

        # 关联部门
        if userInfo.org_codes:
            other_orgs = [
                UserToOrg(
                    user_id=user.id,
                    org_code=str(org_id),
                    primary_flag=False
                )
                for org_id in userInfo.org_codes
            ]
            db.add_all(other_orgs)

        db.commit()



        return create_response(code=200, data=True)

    except Exception as e:
        db.rollback()
        LogUtils.error(f"后台注册异常: {str(e)}")
        return create_response(code=500, message=f"后台注册异常 {str(e)}")


def userBackendUpdate(db: Session, userInfo: UserBackendCreateRequest):
    try:
        # 1. 查询用户是否存在
        user = db.query(User).filter(User.id == userInfo.id).first()
        if not user:
            raise Exception("用户不存在" )

        # 2. 校验唯一性
        if db.query(User).filter(
            User.police_number == userInfo.police_number,
            User.id != userInfo.id
        ).first():
            raise Exception("警号已存在")

        if db.query(User).filter(
            User.id_card_number == userInfo.id_card_number,
            User.id != userInfo.id
        ).first():
            raise HTTPException("身份证号已存在")

        # 3. 获取组织继承的角色
        org_roles_sql = text("""
            SELECT DISTINCT r.id 
            FROM role_info r
            JOIN org_to_role otr ON r.id = otr.role_id
            JOIN user_to_org uto ON otr.org_code = uto.org_code
            WHERE uto.user_id = :user_id
        """)
        result = db.execute(org_roles_sql, {"user_id": userInfo.id})
        org_role_ids = [row[0] for row in result]
        ## 过滤掉组织角色
        direct_role_ids = [rid for rid in userInfo.role_ids if rid not in org_role_ids]

        try:
            # 4. 删除现有关联
            db.query(UserToRole).filter(UserToRole.user_id == userInfo.id).delete()
            db.query(UserToOrg).filter(UserToOrg.user_id == userInfo.id).delete()

            # 5. 更新用户基本信息
            exclude_fields = {"org_code", "org_codes", "role_ids", "password", "id"}
            user_data = {
                k: v for k, v in userInfo.model_dump().items()
                if k not in exclude_fields
            }
            for key, value in user_data.items():
                setattr(user, key, value)

            # 6. 创建新的角色关联
            if direct_role_ids:
                user_roles = [
                    UserToRole(
                        user_id=user.id,
                        role_id=role_id

                    )
                    for role_id in direct_role_ids
                ]
                db.add_all(user_roles)

            # 7. 创建新的组织关联
            # 主部门
            db.add(UserToOrg(
                user_id=user.id,
                org_code=userInfo.org_code,
                primary_flag=True
            ))

            # 关联部门
            if userInfo.org_codes:
                other_orgs = [
                    UserToOrg(
                        user_id=user.id,
                        org_code=str(org_id),
                        primary_flag=False

                    )
                    for org_id in userInfo.org_codes
                ]
                db.add_all(other_orgs)

            db.commit()
            return create_response(code=200, data=user)

        except SQLAlchemyError as e:
            db.rollback()
            raise Exception(f"编辑用户失败:{str(e)}" )

    except Exception as e:
        db.rollback()
        if isinstance(e, HTTPException):
            return create_response(
                code=e.status_code,
                message=e.detail
            )
        return create_response(
            code=500,
            message=f"更新用户失败: {str(e)}"
        )


# def userBackendUpdate(db: Session, userInfo: UserBackendCreateRequest):
#     try:
#         # 1. 查询用户是否存在
#         user = db.query(User).filter(User.id == userInfo.id).first()
#         if not user:
#             return create_response(code=500, message=f"用户不存在")
#
#         # 2. 校验警号唯一性
#         existing_police = db.query(User).filter(
#             User.police_number == userInfo.police_number,
#             User.id != userInfo.id
#         ).first()
#         if existing_police:
#             return create_response(code=500, message=f"警号已存在")
#
#
#         # 3. 校验身份证号唯一性
#         existing_id_card = db.query(User).filter(
#             User.id_card_number == userInfo.id_card_number,
#             User.id != userInfo.id
#         ).first()
#         if existing_id_card:
#             return create_response(code=500, message=f"身份证号已存在")
#
#             # 获取用户通过组织继承的角色
#             org_roles_sql = """
#                     SELECT DISTINCT r.id
#                     FROM role_info r
#                     JOIN org_to_role otr ON r.id = otr.role_id
#                     JOIN user_to_org uto ON otr.org_code = uto.org_code
#                     WHERE uto.user_id = :user_id
#                 """
#             result = self.db.execute(org_roles_sql, {"user_id": user_id})
#             org_role_ids = [row[0] for row in result]
#             # 过滤掉组织已有的角色
#             direct_role_ids = [rid for rid in role_ids if rid not in org_role_ids]
#             # 删除原有直接绑定角色
#             db.query(UserToRole).filter(UserToRole.user_id == userInfo.id).delete()
#
#
#         # 5. 删除用户组织关联
#         db.query(UserToOrg).filter(UserToOrg.user_id == userInfo.id).delete()
#
#         # 6. 更新用户基本信息
#         user_dict = userInfo.model_dump()
#         exclude_fields = {"org_code", "org_codes", "role_ids", "password", "id"}
#         user_data = {k: v for k, v in user_dict.items() if k not in exclude_fields}
#
#         # if userInfo.password:  # 如果提供了新密码
#         #     user_data["hashed_password"] =  pwd_context.hash(userInfo.password)
#         # else:
#         #     user_data["hashed_password"] =user.hashed_password
#         for key, value in user_data.items():
#             setattr(user, key, value)
#
#         # 7. 创建新的用户-角色关联
#         if direct_role_ids:
#             user_roles = [
#                 UserToRole(user_id=user.id, role_id=role_id)
#                 for role_id in direct_role_ids
#             ]
#             db.add_all(user_roles)
#
#         # 8. 创建新的用户-组织关联
#         # 主部门
#         primary_org = UserToOrg(
#             user_id=user.id,
#             org_code=userInfo.org_code,
#             primary_flag=True
#         )
#         db.add(primary_org)
#
#         # 关联部门
#         if userInfo.org_codes:
#             other_orgs = [
#                 UserToOrg(
#                     user_id=user.id,
#                     org_code=str(org_id),
#                     primary_flag=False
#                 )
#                 for org_id in userInfo.org_codes
#             ]
#             db.add_all(other_orgs)
#
#         db.commit()
#         return create_response(code=200, data=user)
#
#     except Exception as e:
#         db.rollback()
#         return create_response(code=500, message=f"更新用户失败: {str(e)}")


def userBackenddeleteUser(db: Session, userId: int):
    try:
        # 查询用户是否存在
        user = db.query(User).filter(User.id == userId).first()
        if not user:
            raise Exception("用户不存在")
        # 执行逻辑删除
        user.is_delete = 1
        db.commit()
        return create_response(code=200, data=True)
    except Exception as e:
        db.rollback()
        return create_response(code=500, message=f"删除用户失败")



def get_user_queryable_orgs(db: Session, user_id: int) -> List[str]:
    """获取用户可查询的组织ID列表
    1. 获取用户主部门
    2. 如果主部门可以查询子部门，则获取所有子部门
    3. 通过org_code前缀匹配获取所有相关组织ID
    """

    # 获取用户主部门信息
    primary_org = db.query(UserToOrg).filter(
        UserToOrg.user_id == user_id,
        UserToOrg.primary_flag == True
    ).first()

    if not primary_org:
        return []

    # 查询主部门详细信息
    org = db.query(Org).filter(Org.org_code == primary_org.org_code).first()
    if not org:
        return []

    queryable_orgs = []
    if org.can_sub_query:
        # 如果可以查询子部门，通过org_code前缀匹配获取所有子部门
        sub_orgs = db.query(Org).filter(
            Org.org_code.like(f"{org.org_code}%")
        ).all()
        queryable_orgs = [org.org_code for org in sub_orgs]
    else:
        # 如果不能查询子部门，只返回主部门ID
        queryable_orgs = [org.org_code]
    return queryable_orgs


def orgCodeChild(db: Session, org_code: str) -> List[str]:
    try:
        # 查询所有匹配的组织机构编码
        org_codes = db.query(Org.org_code) \
            .filter(Org.org_code.like(f"{org_code}%")) \
            .all()

        # 转换查询结果为列表
        return [code[0] for code in org_codes]

    except Exception as e:
        return create_response(code=500, message=f"查询子节点失败: {str(e)}")


def userBackendQueryList(db: Session, req: UserBackendQueryRequest):
    try:
        # 获取分页参数
        page = req.page if req.page > 0 else 1
        page_size = req.page_size if req.page_size > 0 else 10
        offset = (page - 1) * page_size

        ## 查询用户
        userId = UserContext.get_current_id()
        # 获取组织机构ID
        if req.org_codes and len(req.org_codes) > 0:
            org_ids= get_user_queryable_orgs(db, userId)
            if hasattr(req, 'org_codes') and req.org_codes:
                filtered_orgCodes = []
                for org_code in org_ids:
                    # 检查是否有任何一个入参前缀匹配
                    if any(org_code.startswith(prefix) for prefix in req.org_codes):
                        filtered_orgCodes.append(org_code)
                org_ids = filtered_orgCodes
        else:
            org_ids = get_user_queryable_orgs(db, userId)

        if not org_ids:
            return {
                "total": 0,
                "items": [],
                "page": page,
                "page_size": page_size
            }

        # 构建查询条件
        where_conditions = ["id IN (SELECT user_id FROM user_to_org uto WHERE org_code IN :org_codes)", "is_delete = 0"]
        params = {'org_codes': tuple(org_ids)}

        # 添加警号条件
        if req.police_number:
            where_conditions.append("police_number LIKE :police_number")
            params['police_number'] = f"%{req.police_number}%"

        # 添加用户名条件
        if req.username:
            where_conditions.append("username LIKE :username")
            params['username'] = f"%{req.username}%"

        # 组合WHERE条件
        where_clause = " AND ".join(where_conditions)

        # 查询总数
        sqlTotal = text(f"""
            SELECT COUNT(DISTINCT ui.id) as total 
            FROM user_info ui 
            WHERE {where_clause}
        """)
        total = db.execute(sqlTotal, params).scalar()

        # 查询数据
        sql = text(f"""
            SELECT * ,
                 (select  GROUP_CONCAT(oi.name SEPARATOR ',')    
                 from  user_to_org uto
                 left join org_info oi  on  uto.org_code =oi.org_code
                 where uto.primary_flag =1 and uto.user_id =ui.id) as deptName,
             (SELECT GROUP_CONCAT(ri.name SEPARATOR ',')  
     FROM (
         SELECT role_id FROM user_to_role utr WHERE utr.user_id = ui.id
         UNION
         SELECT role_id FROM org_to_role otr 
         WHERE org_code IN (SELECT org_code FROM user_to_org uto WHERE user_id = ui.id)
     ) combined_roles
     LEFT JOIN role_info ri ON ri.id = combined_roles.role_id
    ) AS roleNames
         
            FROM user_info ui 
            WHERE {where_clause}
            ORDER BY ui.id DESC
            LIMIT :limit OFFSET :offset
        """)

        # 添加分页参数
        params.update({
            "limit": page_size,
            "offset": offset
        })

        result = db.execute(sql, params).fetchall()
        users = []
        for row in result:
            user_dict = dict(row._mapping)
            if 'hashed_password' in user_dict:
                del user_dict['hashed_password']
            users.append(user_dict)

        return {
            "items": users,
            "total": total,
            "page": page,
            "page_size": page_size
        }

    except Exception as e:
        return create_response(code=500, message=f"{str(e)}")



def getByIdUser(db: Session, userId: int):
    try:
        sql = text("""
                  SELECT ui.*, 
    (SELECT org_code 
     FROM user_to_org uto 
     WHERE uto.user_id = ui.id AND uto.primary_flag = 1) as orgCodes,
    
    (SELECT JSON_ARRAYAGG(org_code) 
     FROM user_to_org uto 
     WHERE uto.user_id = ui.id AND uto.primary_flag = 0) as orgCodes2,
    
    (SELECT JSON_ARRAYAGG(role_id) 
     FROM (
         SELECT role_id 
         FROM user_to_role 
         WHERE user_id = ui.id
         UNION
         SELECT role_id 
         FROM org_to_role otr 
         WHERE org_code IN (
             SELECT org_code 
             FROM user_to_org uto 
             WHERE user_id = ui.id
         )
     ) AS combined_roles) as roleids
FROM user_info ui 
WHERE ui.id = :userId
        """)

        result = db.execute(sql, {'userId': userId}).fetchone()
        if not result:
            return None
        user_dict = dict(result._mapping)
        # 处理 JSON 数组字段
        import json
        user_dict['org_code'] = str(user_dict['orgCodes']) if user_dict['orgCodes'] else ""  # 直接转换为字符串
        user_dict['org_codes'] = json.loads(user_dict['orgCodes2']) if user_dict['orgCodes2'] else []
        user_dict['role_ids'] = json.loads(user_dict['roleids']) if user_dict['roleids'] else []
        # 移除敏感和不需要的字段
        fields_to_remove = ['hashed_password', 'orgCodes', 'orgCodes2']
        for field in fields_to_remove:
            if field in user_dict:
                del user_dict[field]

        return  create_response(code=200, data=user_dict)

    except Exception as e:
        return create_response(code=500, message=f" {str(e)}")



def UserBackendResetPwd(db: Session, request: UserBackendResetPwdRequest):
    try:
        # 查询用户是否存在
        user = db.query(User).filter(User.id == request.userId).first()
        if not user:
            return create_response(code=500, message="用户不存在")


        # 处理密码逻辑
        if not request.password and not request.password2:
            # 两个密码都为空，设置默认密码
            new_password = "123456"
        else:
            # 至少有一个密码不为空，检查密码一致性
            if request.password != request.password2:
                return create_response(code=500, data="两次输入的密码不一致")

            new_password = request.password

        # 更新密码
        user.hashed_password = pwd_context.hash(new_password)
        db.commit()

        return create_response(code=200);
    except Exception as e:
        db.rollback()
        return create_response(code=500, data=str(e))



def batchList(db: Session, list: List[Dict]) -> bool:
    try:
        # 检查是否为初始化
        is_init = db.query(Org).count() == 0

        # 批量创建组织和字典数据
        org_list = []
        dict_list = []

        for item in list:
            # 创建组织对象
            org = Org(
                name=item['name'],
                org_code=item['org_code'],
                sort=1,
                status=1,
                parent_org_code=item['parent_org_code'],
                can_sub_query=1,
                create_time=datetime.now(),
                update_time=datetime.now(),
                owner_user_id=1
            )
            org_list.append(org)

            # 创建字典对象
            tempOrgInfo= temp_org_map(
                org_code= item['org_code'],
                org_name= item['name'],
                origin_code= item['origin_org_code'],
                police_unit= item['name'],
                police_level=item['police_level'],
                origin_partent_code= item['origin_parent_org_code']
            )
            dict_list.append(tempOrgInfo)
        # 批量插入数据
        org_list
        dict_list
        db.bulk_save_objects(org_list)
        db.bulk_save_objects(dict_list)
        db.commit()
        return True

    except Exception as e:
        db.rollback()
        return create_response(code=500, data=f"批量创建失败: {str(e)}")



def getOrgByPoliceCode(db: Session, police_code: str):
    try:
        # 查询映射表
        mapping = db.query(temp_org_to_user).filter(
            temp_org_to_user.person_code == police_code
        ).first()

        if not mapping:
            return create_response(code=500, message="未找到对应的组织映射信息，请联系管理员")

        # 查询组织信息
        org_info = db.query(temp_org_map).filter(
            temp_org_map.origin_code == mapping.origin_org_code
        ).first()

        if not org_info:
            return create_response(code=500, message="未找到对应的警综单位信息，请联系管理员")

        result= {
            "id": org_info.id,
            "org_code": org_info.org_code,
            "org_name": org_info.org_name,
            "origin_code": org_info.origin_code,
            "police_unit": org_info.police_unit
        }
        # 返回组织信息
        return  create_response(code=200, data=result)

    except Exception as e:
        return create_response(code=500, message=f"查询组织信息失败: {str(e)}")



def upBatchOrgToRole(db: Session):
    try:
        # 查询法制部门
        law_orgs = db.query(Org).filter(
            or_(
                Org.name.like('%法制支队%'),
                Org.name.like('%法制大队%')
            )
        ).all()

        # 查询看守所
        detention_orgs = db.query(Org).filter(
            Org.name.like('%看守所%')
        ).all()

        # 查询情报指挥部门
        intelligence_orgs = db.query(Org).filter(
            Org.name.like('%情报指挥%')
        ).all()

        # 获取所有机构的org_code
        law_org_codes = [org for org in law_orgs]
        detention_org_codes = [org for org in detention_orgs]
        intelligence_org_codes = [org for org in intelligence_orgs]
        # 组装法制部门数据
        r1=db.query(Role).filter(Role.name=='法制').first()
        law_org_roles = [
            OrgToRole(
                org_code=org.org_code,
                role_id=r1.id,  # 假设法制部门角色ID为1
                org_id=org.id
            ) for org in law_org_codes
        ]







        r2=db.query(Role).filter(Role.name == '看守所').first()
        # 组装看守所数据
        detention_org_roles = [
            OrgToRole(
                org_code=org.org_code,
                role_id=r2.id,  # 假设看守所角色ID为2
                org_id=org.id
            ) for org in detention_org_codes
        ]
        r3=db.query(Role).filter(Role.name == '情报指挥中心').first()
        # 组装情报指挥部门数据
        intelligence_org_roles = [
            OrgToRole(
                org_code=org.org_code,
                role_id=r3.id,  # 假设情报指挥角色ID为3
                org_id=org.id
            ) for org in intelligence_org_codes
        ]

        # 批量插入数据
        all_org_roles = law_org_roles + detention_org_roles + intelligence_org_roles
        db.bulk_save_objects(all_org_roles)
        db.commit()
        return {
            'law_orgs': law_org_codes,
            'detention_orgs': detention_org_codes,
            'intelligence_orgs': intelligence_org_codes
        }

    except Exception as e:
        return create_response(code=500, data=f"查询机构信息失败: {str(e)}")


def upBatchUserToOrg(db: Session):
    try:
        # 1. 查询所有用户
        users = db.query(User).all()

        # 2. 批量处理用户数据
        user_org_mappings = []
        for user in users:
            # 根据警号查询原始组织关系
            org_user = db.query(temp_org_to_user).filter(
                 temp_org_to_user.person_code == user.police_number
            ).first()

            if org_user:
                # 查询新的组织编码
                org_map = db.query(temp_org_map).filter(
                    temp_org_map.origin_code == org_user.origin_org_code
                ).first()

                if org_map:
                    # 组装新的用户-组织关系
                    user_org = UserToOrg(
                        user_id=user.id,
                        org_code=org_map.org_code,
                        primary_flag=1,  # 设置为主部门

                    )
                    user_org_mappings.append(user_org)

        # 3. 批量插入数据
        if user_org_mappings:
            # 先删除已存在的关联关系
            user_ids = [mapping.user_id for mapping in user_org_mappings]
            db.query(UserToOrg).filter(
                UserToOrg.user_id.in_(user_ids)
            ).delete(synchronize_session=False)

            # 插入新的关联关系
            db.bulk_save_objects(user_org_mappings)
            db.commit()

        return {
            'total_users': len(users),
            'mapped_users': len(user_org_mappings),
            'unmapped_users': len(users) - len(user_org_mappings)
        }

    except Exception as e:
        db.rollback()
        return create_response(code=500, data=f"恢复用户机构关系失败: {str(e)}")
