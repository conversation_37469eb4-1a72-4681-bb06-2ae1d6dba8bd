from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.common.response_com import create_response
from app.core.permission_check import require_permissions
from app.db.database import get_db
from app.schemas.user_schema import UserBackendCreateRequest, \
    UserBackendUpdateRequest, UserBackendQueryRequest, UserBackendResetPwdRequest
from app.services.user_service import userBackendCreate, userBackendUpdate, userBackenddeleteUser, \
    userBackendQueryList, getByIdUser, UserBackendResetPwd  # 添加导入

router = APIRouter(prefix="/user/backend", tags=["menu"])


@router.post("/register", description="用户注册")
@require_permissions(permission_key="user:register")
def register_user(
        request: UserBackendCreateRequest,
        db: Session = Depends(get_db)
):
    user = userBackendCreate(db, request)
    return create_response(code=200, data=user)


@router.post("/update_user", description="用户编辑")
@require_permissions(permission_key="user:update")
def update_user(
        request: UserBackendUpdateRequest,
        db: Session = Depends(get_db)
):
    return userBackendUpdate(db, request)


@router.get("/delete_user/{userId}", description="用户编辑")
@require_permissions(permission_key="user:delete")
def delete_user(
        userId: int, db: Session = Depends(get_db)
):
    return userBackenddeleteUser(db, userId)


@router.post("/list", description="用户编辑")
@require_permissions(permission_key="user:list")
def list_user(request: UserBackendQueryRequest, db: Session = Depends(get_db)):
    result = userBackendQueryList(db, request)
    return create_response(code=200, data=result)


@router.get("/byId/{userId}", description="用户编辑")
@require_permissions(permission_key="user:byId")
def get_byid_user(userId: int, db: Session = Depends(get_db)):
    return getByIdUser(db, userId)


@router.post("/resetPwd", description="重置密码")
@require_permissions(permission_key="user:changePwd")
def change_password(
        request: UserBackendResetPwdRequest,
        db: Session = Depends(get_db)
):
    return UserBackendResetPwd(db, request)
