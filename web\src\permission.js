import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/auth";
import { isPathMatch } from "@/utils/validate";
// import { isRelogin } from '@/utils/request'

/**
 * 递归查找第一个可访问的路由
 * @param {Array} routes 路由数组
 * @returns {Object|null} 第一个可访问的路由对象
 */
const findFirstAccessibleRoute = routes => {
  if (!routes || routes.length === 0) return null;

  const firstRoute = routes[0];
  if (firstRoute.children && firstRoute.children.length > 0) {
    return findFirstAccessibleRoute(firstRoute.children);
  }
  return firstRoute;
};

const whiteList = ["/login", "/register"];

const isWhiteList = path => {
  return whiteList.some(pattern => isPathMatch(pattern, path));
};

router.beforeEach(async (to, from, next) => {
  NProgress.start();

  if (getToken()) {
    if (to.path === "/login") {
      // 获取动态路由
      const accessRoutes = store.getters.permission_routes;
      // 找到第一个可访问的路由
      const firstRoute = findFirstAccessibleRoute(accessRoutes);
      if (firstRoute) {
        next({ path: firstRoute.path });
      } else {
        next({ path: "/" });
      }
      NProgress.done();
    } else {
      // 判断是否已经加载过动态路由
      const hasRoutes =
        store.getters.permission_routes && store.getters.permission_routes.length > 0;

      if (hasRoutes) {
        next();
      } else {
        try {
          // 获取动态路由
          const accessRoutes = await store.dispatch("permission/GenerateRoutes");
          // 动态添加路由
          router.addRoutes(accessRoutes);

          // 使用 replace: true 来替换当前历史记录
          next({ ...to, replace: true });
        } catch (error) {
          // 发生错误时，清除token并跳转到登录页
          await store.dispatch("logout");
          Message.error(error || "获取路由失败");
          next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
          NProgress.done();
        }
      }
    }
  } else {
    if (isWhiteList(to.path)) {
      next();
    } else {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
