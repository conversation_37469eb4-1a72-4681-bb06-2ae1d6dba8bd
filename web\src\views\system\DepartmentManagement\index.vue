<template>
  <div class="app-container bigscreen">
    <el-row :gutter="20" class="app-container-row">
      <splitpanes :horizontal="false" class="default-theme">
        <!--部门数据-->
        <pane size="16" min-size="16">
          <el-col class="dep-wrapper">
            <div class="search-container">
              <el-input
                v-model="deptName"
                placeholder="请输入部门名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
              />
            </div>
            <div class="org-box">
              <span><i class="el-icon-s-home"></i></span>
              <span>部门结构</span>
            </div>
            <div class="head-container">
              <el-tree
                :data="deptOptions"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                :default-expanded-keys="defaultExpandedKeys"
                ref="tree"
                node-key="id"
                highlight-current
                @node-click="handleNodeClick"
              >
                <span slot-scope="{ node }" class="custom-tree-node">
                  <element-tree-line :node="node" :showLabelLine="false" :indent="16">
                    <span v-if="!deptName">{{ node.label }}</span>
                    <span
                      v-if="deptName"
                      v-html="
                        node.label.replace(
                          new RegExp(deptName, 'g'),
                          `<font style='color:rgb(0, 255, 255, 0.8)'>${deptName}</font>`
                        )
                      "
                    />
                  </element-tree-line>
                </span>
              </el-tree>
            </div>
            <OptionBox
              :currentDepartment="currentDepartment"
              v-if="currentDepartment.id"
              @addCallback="addCallback"
              @updateCallback="updateCallback"
              @deleteCallback="deleteCallback"
            />
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="84">
          <el-col style="display: flex; flex-direction: column; height: 100%">
            <div class="search-section">
              <el-form
                :model="queryParams"
                ref="queryForm"
                :inline="true"
                v-show="showSearch"
                class="search-form common-search depart-search"
              >
                <div class="search-left">
                  <el-form-item label="用户名称" prop="username">
                    <el-input
                      v-model="queryParams.username"
                      placeholder="请输入用户名称"
                      clearable
                      @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="警号" prop="police_number">
                    <el-input
                      v-model="queryParams.police_number"
                      placeholder="请输入警号"
                      clearable
                      @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="状态" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="用户状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item> -->
                  <!-- <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item> -->
                </div>

                <div class="search-right">
                  <el-form-item>
                    <el-button type="primary" @click="handleQuery">搜索</el-button>
                    <el-button type="primary" @click="resetQuery">重置</el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
            <div class="common-action-button-operations">
              <div class="left-operations">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  class="tool-btn no-active"
                  @click="handleAdd"
                  style="margin-bottom: 10px"
                  size="mini"
                >
                  新增
                </el-button>
              </div>
            </div>
            <div class="table-container">
              <el-table
                v-loading="loading"
                :data="userList"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="60" align="center" />
                <el-table-column
                  label="用户名称"
                  align="center"
                  key="username"
                  prop="username"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="警号"
                  align="center"
                  key="police_number"
                  prop="police_number"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="所属部门"
                  align="center"
                  key="deptName"
                  prop="deptName"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="角色"
                  align="center"
                  key="roleNames"
                  prop="roleNames"
                  :show-overflow-tooltip="true"
                />
                <el-table-column label="状态" align="center" key="status" width="80">
                  <template slot-scope="scope">
                    <el-switch
                      v-model="scope.row.is_active"
                      active-color="#00ffffbc"
                      :active-value="1"
                      :inactive-value="0"
                      @change="handleStatusChange(scope.row)"
                    ></el-switch>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                label="创建时间"
                align="center"
                prop="createTime"
                v-if="columns[6].visible"
                width="160"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column> -->
                <el-table-column
                  label="操作"
                  align="center"
                  width="350"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      class="view-btn"
                      @click="handleUpdate(scope.row)"
                    >
                      修改
                    </el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      class="view-btn"
                      @click="handleDelete(scope.row)"
                    >
                      删除
                    </el-button>
                    <!-- <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-refresh"
                    @click="handleReSetPwd(scope.row)"
                  >
                    重置密码
                  </el-button> -->
                    <el-dropdown
                      size="mini"
                      @command="command => handleCommand(command, scope.row)"
                    >
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-d-arrow-right"
                        class="view-btn"
                      >
                        更多
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="handleResetPwd" icon="el-icon-key">
                          重置密码
                        </el-dropdown-item>
                        <!-- <el-dropdown-item
                        command="handleAuthRole"
                        icon="el-icon-circle-check"
                        v-hasPermi="['system:user:edit']"
                      >
                        分配角色
                      </el-dropdown-item> -->
                      </el-dropdown-menu>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.page"
                :limit.sync="queryParams.page_size"
                @pagination="getList"
              />
            </div>
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="600px"
      custom-class="abnormal-dialog screen-dialog"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="用户名称" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="警号" prop="police_number">
              <el-input v-model="form.police_number" placeholder="请输入警号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属部门" prop="org_code">
              <treeselect
                v-model="form.org_code"
                :options="enabledDeptOptions"
                :show-count="true"
                :normalizer="normalizerCode"
                placeholder="请选择归属部门"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="身份证号" prop="id_card_number">
              <el-input v-model="form.id_card_number" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="手机号码" prop="phone_number">
              <el-input v-model="form.phone_number" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="角色" prop="role_ids">
              <el-select
                v-model="form.role_ids"
                :options="roleOptions"
                placeholder="选择角色"
                style="width: 100%"
                multiple
              >
                <el-option
                  v-for="item in roleOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="跨部门" prop="org_codes">
              <treeselect
                v-model="form.org_codes"
                :options="enabledDeptOptions"
                :show-count="true"
                :normalizer="normalizerCode"
                :multiple="true"
                placeholder="请选择跨部门"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="!form.id">
          <el-col :span="24">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入密码"
                type="password"
                maxlength="20"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-radio-group v-model="form.is_active">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="ensure-btn" @click="submitForm">确 定</el-button>
        <el-button type="primary" class="cancel-btn" @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
          >
            下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// or 局部注册组件
import ElementTreeLine from "element-tree-line";
// css
import "element-tree-line/dist/style.css";
import { getToken } from "@/utils/auth";
import {
  getDepartment,
  getRole,
  getUserList,
  addUser,
  updateUser,
  deleteUser,
  getUserById,
  resetUserPwd,
} from "@/api/department";
import OptionBox from "./components/OptionBox";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  name: "User",
  // dicts: ["sys_normal_disable", "sys_user_sex"],
  components: { Treeselect, Splitpanes, Pane, OptionBox, ElementTreeLine },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 所有部门树选项
      deptOptions: undefined,
      // 过滤掉已禁用部门树选项
      enabledDeptOptions: undefined,
      // 默认展开的节点
      defaultExpandedKeys: [],
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "name",
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        page: 1,
        page_size: 10,
        username: undefined,
        police_number: undefined,
        // status: undefined,
        // deptId: undefined,
      },
      // 列信息
      columns: [
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `警号`, visible: true },
        { key: 3, label: `所属部门`, visible: true },
        { key: 4, label: `角色`, visible: true },
        { key: 5, label: `状态`, visible: true },
        // { key: 6, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        username: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: "用户名称长度必须介于 2 和 20 之间", trigger: "blur" },
        ],
        police_number: [{ required: true, message: "警号不能为空", trigger: "blur" }],
        id_card_number: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
          {
            pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
            message: "请输入正确的身份证号",
            trigger: "blur",
          },
        ],
        org_code: [{ required: true, message: "所属部门不能为空", trigger: "blur" }],
        // org_codes: [{ required: true, message: "跨部门不能为空", trigger: "blur" }],
        role_ids: [{ required: true, message: "角色不能为空", trigger: "blur" }],
        is_active: [{ required: true, message: "状态不能为空", trigger: "blur" }],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          { min: 6, message: "密码长度至少为6个字符", trigger: "blur" },
          { max: 12, message: "密码长度至长为12个字符", trigger: "blur" },
        ],
        phone_number: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" },
        ],
      },
      sys_normal_disable: [
        {
          label: "启用",
          value: 1,
        },
        {
          label: "禁用",
          value: 0,
        },
      ],
      sys_user_sex: [
        {
          label: "男",
          value: "0",
        },
        {
          label: "女",
          value: "1",
        },
      ],
      currentDepartment: {},
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    // this.getList();
    this.getDeptTree();
    this.getRoleOptions();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    /** 转换部门数据结构 */
    normalizerId(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children,
      };
    },
    normalizerCode(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.org_code,
        label: node.name,
        children: node.children,
      };
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      getUserList({
        ...this.queryParams,
        org_codes: [this.currentDepartment.org_code],
      })
        .then(res => {
          if (res.code === 200 && res.data) {
            this.userList = res.data.items || [];
            this.total = res.data.total || 0;
            this.loading = false;
          }
        })
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    getRoleOptions() {
      getRole({
        name: "",
        status: "1",
        page_size: 999,
        page: 1,
      })
        .then(res => {
          if (res.code === 200 && res.data) {
            this.roleOptions = res.data.items || [];
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    findDeptById(id, deptList = this.deptOptions) {
      for (const dept of deptList) {
        if (dept.id === id) {
          return dept;
        }
        if (dept.children && dept.children.length > 0) {
          const result = this.findDeptById(id, dept.children);
          if (result) {
            return result;
          }
        }
      }
      return null;
    },

    addCallback() {
      this.getDeptTree(false);
    },
    updateCallback() {
      this.getDeptTree(false);
    },
    deleteCallback() {
      this.getDeptTree(true);
    },

    /** 查询部门下拉树结构 */
    getDeptTree(isRefresh = true) {
      getDepartment()
        .then(response => {
          if (response.code === 200) {
            this.deptOptions = response.data;
            this.enabledDeptOptions = this.filterDisabledDept(response.data);
            // 设置默认展开的一级节点
            this.defaultExpandedKeys = this.deptOptions.map(item => item.id);
            this.$nextTick(() => {
              if (this.deptOptions && this.deptOptions.length > 0) {
                if (isRefresh) {
                  // 默认一进入就走这个，或者删除一个部门后
                  this.currentDepartment = this.deptOptions[0];
                  this.$refs.tree.setCurrentKey(this.deptOptions[0].id);
                } else {
                  const currentDepId = this.currentDepartment.id;
                  const currentDepartment = this.findDeptById(currentDepId);
                  this.currentDepartment = currentDepartment;
                  this.$refs.tree.setCurrentKey(currentDepId);
                }
                this.getList();
              }
            });
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter(dept => {
        if (!dept.status) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      console.log(value, data);
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.currentDepartment = data;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.is_active === 1 ? "启用" : "禁用";
      this.$confirm('确认要"' + text + '""' + row.username + '"用户吗？')
        .then(function () {
          return getUserById(row.id);
        })
        .then(res => {
          // this.$modal.msgSuccess(text + "成功");
          if (res.code === 200 && res.data) {
            updateUser({
              ...res.data,
              is_active: row.is_active,
            })
              .then(result => {
                if (result.code === 200) {
                  this.$message.success(text + "成功");
                }
              })
              .catch(err => {
                row.is_active = row.is_active === 1 ? 0 : 1;
              });
          }
        })
        .catch(function () {
          row.is_active = row.is_active === 1 ? 0 : 1;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        org_code: undefined,
        username: undefined,
        password: undefined,
        phone_number: undefined,
        is_active: 1,
        id_card_number: undefined,
        police_number: undefined,
        role_ids: [],
        org_codes: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // getUser().then(response => {
      //   this.postOptions = response.posts;
      //   this.roleOptions = response.roles;
      //   this.open = true;
      //   this.title = "添加用户";
      //   this.form.password = this.initPassword;
      // });
      // this.postOptions = response.posts;
      // this.roleOptions = response.roles;
      this.open = true;
      this.title = "添加用户";
      this.form.org_code = this.currentDepartment.org_code;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getUserById(row.id)
        .then(res => {
          if (res.code === 200 && res.data) {
            this.form = { ...res.data };
          }
        })
        .catch(err => {
          this.$message.error(err.message);
        });
      this.open = true;
      this.title = "修改用户";
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      console.log(122);
      // this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   closeOnClickModal: false,
      //   inputPattern: /^.{5,20}$/,
      //   inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
      //   inputValidator: value => {
      //     if (/<|>|"|'|\||\\/.test(value)) {
      //       return "不能包含非法字符：< > \" ' \\\ |";
      //     }
      //   },
      // })
      //   .then(({ value }) => {
      //     resetUserPwd(row.userId, value).then(response => {
      //       this.$modal.msgSuccess("修改成功，新密码是：" + value);
      //     });
      //   })
      //   .catch(() => {});
      this.$confirm("确定要重置密码吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          resetUserPwd({
            userId: row.id,
          })
            .then(res => {
              if (res.code === 200) {
                this.$message.success("重置密码成功");
              }
            })
            .catch(err => {
              this.$message.error(err.message);
            });
        })
        .catch(() => {});
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const params = {
            ...this.form,
          };
          if (this.form.id != undefined) {
            updateUser(params)
              .then(res => {
                if (res.code === 200 && res.data) {
                  this.$message.success("修改成功");
                  this.open = false;
                  this.getList();
                }
              })
              .catch(err => {
                this.$message.error(err.message);
              });
          } else {
            addUser(params)
              .then(res => {
                if (res.code === 200 && res.data) {
                  this.$message.success("新增成功");
                  this.open = false;
                  this.getList();
                }
              })
              .catch(err => {
                this.$message.error(err.message);
              });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.id;
      this.$confirm('是否确认删除用户名称为"' + row.username + '"的数据项？', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return deleteUser(userIds);
        })
        .then(() => {
          this.getList();
          this.$message.success("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/user/export",
        {
          ...this.queryParams,
        },
        `user_${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download("system/user/importTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
<style lang="less" scoped>
@import url("./index.less");
</style>
