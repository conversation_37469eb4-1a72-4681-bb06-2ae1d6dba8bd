<template>
  <div class="layout-container">
    <!-- 左侧导航栏 (仅在已认证且不是登录/注册页面时显示) -->
    <div
      v-if="!isAuthPage"
      class="sidebar"
      :class="{ collapsed: isSidebarCollapsed }"
    >
      <div class="police-badge" v-show="!isSidebarCollapsed">
        <img src="@/assets/images/gongan.png" alt="公安徽章" class="badge-image" />
        <div class="police-motto">
          <p>对党忠诚 服务人民</p>
          <p>执法公正 纪律严明</p>
        </div>
      </div>
      <div class="police-badge" v-show="isSidebarCollapsed">
        <img src="@/assets/images/gongan.png" alt="公安徽章" class="badge-image-left" />
      </div>
      <div class="sidebar-content">
        <ul class="nav-menu">
          <li
            class="nav-item"
            v-for="(nav, index) in routerList"
            :key="index"
            :title="isSidebarCollapsed ? nav.title : ''"
          >
            <router-link
              :to="nav.path"
              :exact="nav.path === '/index'"
              v-if="!nav.children || nav.children.length === 0"
            >
              <svg-icon
                :icon-class="$route.path === nav.path ? nav.meta.icon + '-active' : nav.meta.icon"
              />
              <span v-show="!isSidebarCollapsed">{{ nav.title }}</span>
            </router-link>
            <a
              v-else
              @click.stop="toggleSubMenu(index)"
              :class="{ 'router-link-active': isSubMenuActive(nav) }"
            >
              <svg-icon
                :icon-class="isSubMenuActive(nav) ? nav.meta.icon + '-active' : nav.meta.icon"
              />
              <span v-show="!isSidebarCollapsed" style="cursor: pointer">{{ nav.title }}</span>
              <i
                class="el-icon-arrow-down"
                v-if="nav.children"
                v-show="!isSidebarCollapsed"
                :class="{ 'is-expanded': expandedMenus[index] }"
              ></i>
            </a>
            <template v-if="nav.children && nav.children.length > 0">
              <ul class="nav-menu-children" v-show="expandedMenus[index]">
                <li v-for="(child, childIndex) in nav.children" :key="childIndex">
                  <router-link :to="child.path">
                    <span>{{ child.meta.title }}</span>
                  </router-link>
                </li>
              </ul>
            </template>
          </li>
        </ul>
      </div>
      <div class="sidebar-footer">
        <div class="user-info" v-show="!isSidebarCollapsed">
          <img src="@/assets/images/svgs/编组.svg" class="user-avatar" alt="" />
          <div class="user-details">
            <div class="user-name">
              {{ username }}
              <div class="user-police-number">&nbsp;- {{ police_number }}</div>
            </div>

            <el-tooltip :content="police_unit" placement="top" :disabled="!isTextOverflow">
              <div class="user-unit" ref="unitText">{{ police_unit }}</div>
            </el-tooltip>
          </div>
        </div>
        <el-button v-if="!isSidebarCollapsed" type="text" class="logout-button" @click="logout">
          <i class="el-icon-switch-button"></i>
          <span>退出登录</span>
        </el-button>
        <el-button v-else type="text" class="logout-button-small" @click="logout">
          <i class="el-icon-switch-button"></i>
        </el-button>
      </div>
      <!-- 收起/展开按钮 -->
      <div class="sidebar-toggle" @click="toggleSidebar">
        <i :class="isSidebarCollapsed ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
      </div>
    </div>

    <!-- 延迟加载聊天组件，避免循环依赖 -->
    <!-- <component v-if="showChat && isAuthenticated && !isAuthPage" :is="chatComponent" /> -->

    <!-- 主内容区 -->
    <div
      class="main-content"
      :class="{
        'with-sidebar': isAuthenticated && !isAuthPage,
        'with-collapsed-sidebar': isSidebarCollapsed && isAuthenticated && !isAuthPage,
        'full-width': !isAuthenticated || isAuthPage,
      }"
    >
      <router-view />
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import SvgIcon from "@/components/SvgIcon";

export default {
  name: "Layout",
  components: {
    SvgIcon,
  },
  data() {
    return {
      showChat: true,
      chatComponent: null,
      isSidebarCollapsed: false,
      isTextOverflow: false,
      expandedMenus: {},
      routerList: [],
    };
  },
  computed: {
    ...mapGetters(["isAuthenticated"]),
    routes() {
      return this.$store.state.permission.routes;
    },
    navRoutes() {
      return this.routes.find(route => route.path === "/")?.children || [];
    },
    police_number() {
      return this.$store.state.user ? this.$store.state.user.police_number : "";
    },
    username() {
      return this.$store.state.user ? this.$store.state.user.username : "";
    },
    police_unit() {
      return this.$store.state.user ? this.$store.state.user.police_unit : "";
    },
    isAuthPage() {
      return (
        this.$route.path === "/login" ||
        this.$route.path === "/register" ||
        this.$route.path === "/reset-password" ||
        this.$route.path === "/security-notice" ||
        this.$route.path === "/file-review/preview"
      );
    },
  },
  async created() {
    // 从store中获取路由
    this.routerList = this.navRoutes;
    // console.log("路由信息：", JSON.stringify(this.routerList));

    // 异步加载聊天组件，避免循环依赖
    import("@/components/AIChatBox.vue").then(module => {
      this.chatComponent = module.default;
    });

    // 从本地存储中恢复侧边栏状态
    const savedState = localStorage.getItem("sidebarCollapsed");
    if (savedState !== null) {
      this.isSidebarCollapsed = JSON.parse(savedState);
    }

    this.isDevEnv = process.env.NODE_ENV === "development";

    // await this.loadImg();
  },
  mounted() {
    // 检查文字是否溢出
    this.checkTextOverflow();
    // 监听窗口大小变化
    window.addEventListener("resize", this.checkTextOverflow);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.checkTextOverflow);
  },
  methods: {
    isSubMenuActive(nav) {
      if (!nav.children || nav.children.length === 0) {
        return this.$route.path === nav.path;
      }
      return nav.children.some(child => this.$route.path === child.path);
    },
    async logout() {
      try {
        await this.$store.dispatch("logout");
        this.$message({
          message: "已成功退出登录",
          type: "success",
        });
        // 退出登录后跳转到登录页
        this.$router.push("/login");
      } catch (error) {
        console.error("退出登录出错:", error);
      }
    },
    toggleSidebar() {
      this.isSidebarCollapsed = !this.isSidebarCollapsed;
      // 保存状态到本地存储
      localStorage.setItem("sidebarCollapsed", JSON.stringify(this.isSidebarCollapsed));
    },
    checkTextOverflow() {
      if (this.$refs.unitText) {
        const element = this.$refs.unitText;
        this.isTextOverflow = element.scrollWidth > element.clientWidth;
      }
    },
    toggleSubMenu(index) {
      this.$set(this.expandedMenus, index, !this.expandedMenus[index]);
    },
  },
};
</script>

<style scoped lang="less">
/* @import '@/assets/css/reset.css'; */

.layout-container {
  width: 100%;
  height: 100vh;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  display: flex;
  overflow: hidden;
  background-image: linear-gradient(180deg, #031d2e 0%, #031d2e 14%);
}

* {
  box-sizing: border-box;
}

.sidebar {
  width: 260px;
  height: 100vh;
  border-right: 1px solid #06445d;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  background-image: linear-gradient(180deg, #031d2e 0%, #091822 14%);
}

.sidebar.collapsed {
  width: 60px;
  background: #031f2e;
}

.sidebar-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  /* min-height: 80px; */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #00ffff;
  margin-bottom: 5px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  letter-spacing: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar.collapsed .logo {
  font-size: 14px;
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(0, 255, 255, 0.6);
  letter-spacing: 1px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
  background-color: #031d2e;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  .nav-menu-children {
    list-style: none;
    // padding-left: 20px;
    margin: 0;
    // margin-left: 20px;
    li {
      a {
        padding-left: 50px;
      }
    }
  }
}

.nav-item {
  margin-bottom: 5px;

  .el-icon-arrow-down {
    transition: transform 0.3s;
    margin-left: auto;
    cursor: pointer;

    &.is-expanded {
      transform: rotate(180deg);
    }
  }
}

.nav-item a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s;
  border-left: 3px solid transparent;
  font-size: 14px;
  white-space: nowrap;
}

.sidebar.collapsed .nav-item a {
  padding: 12px 0;
  justify-content: center;
}

.nav-item a i {
  font-size: 18px;
  margin-right: 10px;
  width: 24px;
  text-align: center;
  color: #fff;
}

.sidebar.collapsed .nav-item a i {
  margin-right: 0;
}

.nav-item > a.router-link-active {
  color: #00ffff;
  background: rgba(0, 255, 255, 0.1);
}

.sidebar.collapsed .nav-item a.router-link-active {
  border-left-color: transparent;
}

.sidebar.collapsed .nav-item {
  img {
    margin-right: 0px;
  }
}

.nav-item a.router-link-active i {
  color: #00ffff;
}

.nav-item img {
  width: 18px;
  margin-right: 12px;
}

.nav-item a:hover {
  color: #00ffff;
  background: rgba(0, 255, 255, 0.05);
}

.sidebar-footer {
  padding: 15px 20px;
  background-color: #031d2e;
}

.sidebar.collapsed .sidebar-footer {
  padding: 15px 10px;
  display: flex;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px 0;
}

.user-avatar {
  width: 44px;
  height: 44px;
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 32px;
  margin-left: 20px;
}

.user-name {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
}

.user-police-number {
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-unit {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: default;
}

.logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 255, 255, 0.8) !important;
  background: rgba(0, 255, 255, 0.1) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 4px !important;
  padding: 8px 0 !important;
  font-size: 14px !important;
  transition: all 0.3s !important;
  margin-left: 0px !important;
}

.logout-button-small {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 255, 255, 0.8) !important;
  background: rgba(0, 255, 255, 0.1) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 4px !important;
  padding: 0 !important;
  font-size: 14px !important;
  transition: all 0.3s !important;
  margin-left: 0px !important;
}

.logout-button i,
.logout-button-small i {
  margin-right: 5px;
  font-size: 16px;
}

.logout-button-small i {
  margin-right: 0;
}

.logout-button:hover,
.logout-button-small:hover {
  color: #00ffff !important;
  background: rgba(0, 255, 255, 0.15) !important;
  border-color: rgba(0, 255, 255, 0.3) !important;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.1) !important;
}

.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  background: rgba(0, 24, 51, 0.9);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1001;
  color: rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.sidebar-toggle:hover {
  background: rgba(0, 36, 77, 0.9);
  color: #00ffff;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.sidebar-toggle i {
  font-size: 14px;
}

.main-content {
  flex: 1;
  /* overflow-y: auto; */
  position: relative;
  transition: all 0.3s ease;
  background: url("@/assets/images/bg.png") no-repeat center center;
  background-size: cover;
}

.main-content.with-sidebar {
  width: calc(100% - 260px);
}

.main-content.with-collapsed-sidebar {
  width: calc(100% - 60px);
}

.main-content.full-width {
  width: 100%;
}

/* 自定义滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #031d2e;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-track {
  background-color: #031d2e;
  border-radius: 2px;
}

.main-content::-webkit-scrollbar {
  width: 4px;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
}

.main-content::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.05);
  border-radius: 2px;
}

.police-badge {
  /* padding: 0 15px; */
  /* margin: 20px 0 30px; */
  /* background-image: linear-gradient(180deg, #2B5C6E 0%, #031D2E 50%); */
  background-image: url("@/assets/images/left-top.png");
  background-size: cover; /* 背景图铺满容器 */
  background-position: center; /* 居中显示 */
  background-repeat: no-repeat; /* 不重复 */
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.badge-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 10px;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.4));
}

.badge-image-left {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin-bottom: 10px;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.4));
}

.police-motto {
  width: 100%;
  text-align: center;
  color: #ffffff;
  font-size: 13px;
  line-height: 1.5;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
  font-family: "SimSun", serif;
}

.police-motto p {
  margin: 2px 0;
  letter-spacing: 1px;
}

.nav-menu-children {
  list-style: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  transition: all 0.3s ease;

  li {
    a {
      padding-left: 50px;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: #00ffff;
      }

      &.router-link-active {
        color: #00ffff;
        // background: rgba(0, 255, 255, 0.1);
      }
    }
  }
}

.nav-item {
  .svg-icon {
    width: 18px;
    height: 18px;
    margin-right: 12px;
  }
}
</style>
