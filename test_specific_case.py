#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定的王五身份证查询问题
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import SessionLocal
from app.services.scene_qz.scene_qz_service import SceneQzService
from app.log.log_utils import LogUtils


async def test_wangwu_id_card():
    """测试王五身份证查询的特定问题"""
    
    db = SessionLocal()
    try:
        print("="*60)
        print("测试王五身份证查询问题")
        print("="*60)
        
        # 测试用例：王五 + 身份证 ******************
        test_id_card = "******************"
        test_name = "王五"
        
        print(f"查询参数:")
        print(f"  姓名: {test_name}")
        print(f"  身份证: {test_id_card}")
        print()
        
        # 调用优化后的方法
        result = await SceneQzService.search_cases_by_entity(
            id_card=test_id_card,
            name=test_name,
            page=1,
            page_size=10,
            db=db
        )
        
        print(f"查询结果:")
        print(f"  总数: {result['total']}")
        print(f"  返回条目数: {len(result['items'])}")
        print()
        
        if result['items']:
            for i, item in enumerate(result['items'], 1):
                print(f"  聚类 {i}:")
                print(f"    姓名: {item['name']}")
                print(f"    身份证: {item['id_card']}")
                print(f"    案件数量: {len(item['cases'])}")
                print()
                
                for j, case in enumerate(item['cases'], 1):
                    print(f"      案件 {j}: {case['case']}")
                    print(f"        角色: {case['role']}")
                    print(f"        时间: {case['police_time']}")
                    print()
        else:
            print("  ❌ 没有找到任何结果！")
            
        # 验证结果
        if result['total'] > 0:
            print("✅ 测试通过：成功找到王五的案件聚类")
            
            # 验证返回的姓名和身份证是否正确
            first_cluster = result['items'][0]
            if first_cluster['name'] == test_name and first_cluster['id_card'] == test_id_card:
                print("✅ 验证通过：返回的姓名和身份证正确")
            else:
                print(f"❌ 验证失败：期望姓名={test_name}, 身份证={test_id_card}")
                print(f"           实际姓名={first_cluster['name']}, 身份证={first_cluster['id_card']}")
                
            # 验证案件数量
            if len(first_cluster['cases']) >= 2:
                print(f"✅ 验证通过：找到{len(first_cluster['cases'])}个案件（>=2）")
            else:
                print(f"❌ 验证失败：只找到{len(first_cluster['cases'])}个案件（<2）")
        else:
            print("❌ 测试失败：没有找到任何结果")
            
    except Exception as e:
        print(f"❌ 测试执行失败: {str(e)}")
        LogUtils.error(f"测试执行失败: {str(e)}")
        
    finally:
        db.close()


async def test_data_verification():
    """验证数据库中王五的数据"""
    
    db = SessionLocal()
    try:
        print("\n" + "="*60)
        print("验证数据库中王五的原始数据")
        print("="*60)
        
        # 直接查询数据库验证王五的数据
        from sqlalchemy import text
        
        query_sql = """
        SELECT case_id, entity_id, feature_type, feature_value
        FROM qz_case_feature 
        WHERE feature_value = '王五' OR feature_value = '******************'
        ORDER BY case_id, entity_id, feature_type
        """
        
        result = db.execute(text(query_sql))
        rows = result.fetchall()
        
        print(f"数据库中与王五相关的记录数: {len(rows)}")
        print()
        
        for row in rows:
            print(f"  案件: {row[0]}, 实体: {row[1]}, 类型: {row[2]}, 值: {row[3]}")
            
        # 统计案件数量
        case_ids = set()
        for row in rows:
            if row[3] == '王五':  # 只统计姓名为王五的记录
                case_ids.add(row[0])
                
        print(f"\n王五出现的案件数量: {len(case_ids)}")
        print(f"案件列表: {list(case_ids)}")
        
    except Exception as e:
        print(f"❌ 数据验证失败: {str(e)}")
        
    finally:
        db.close()


if __name__ == "__main__":
    print("开始测试王五身份证查询问题...")
    
    # 运行特定测试
    asyncio.run(test_wangwu_id_card())
    
    # 验证原始数据
    asyncio.run(test_data_verification())
    
    print("\n测试完成!")
